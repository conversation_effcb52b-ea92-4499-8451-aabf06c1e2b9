{"__meta": {"id": "01K1RWRR59R3MMDVBAZENZ6TKZ", "datetime": "2025-08-03 22:17:56", "utime": **********.266374, "method": "POST", "uri": "/admin/catalog/products/create", "ip": "127.0.0.1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (42)", "Webkul\\Attribute\\Models\\AttributeFamily (1)"], "views": [], "queries": [{"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'color'", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = '2' limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (6)", "Webkul\\Core\\Models\\Locale (26)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 211", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 212", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 213", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 214", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 215", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (8)", "Webkul\\Product\\Models\\ProductAttributeValue (40)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `products` where `sku` = '8034412526232'", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 212 and `product_attribute_values`.`product_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 212 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 213 and `product_attribute_values`.`product_id` is not null", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 213 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 214 and `product_attribute_values`.`product_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 214 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 215 and `product_attribute_values`.`product_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 215 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1054", "duration": 2.19, "duration_str": "2.19s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1055", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1056", "duration": 2.15, "duration_str": "2.15s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1057", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1058", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 211 and `products`.`parent_id` is not null", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 212 and `product_attribute_values`.`product_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1059", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = '8034412526232-variant-2', `name` = 'Variant 2', `url_key` = '8034412526232-variant-2', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1060", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1061", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1062", "duration": 2.05, "duration_str": "2.05s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1063", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 213 and `product_attribute_values`.`product_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1064", "duration": 2.29, "duration_str": "2.29s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = '8034412526232-variant-496', `name` = 'Variant 496', `url_key` = '8034412526232-variant-496', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1065", "duration": 2.04, "duration_str": "2.04s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1066", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1067", "duration": 2.05, "duration_str": "2.05s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1068", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 214 and `product_attribute_values`.`product_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 214 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-497', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1069", "duration": 2.06, "duration_str": "2.06s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 214 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-497', `description` = '8034412526232-variant-497', `name` = 'Variant 497', `url_key` = '8034412526232-variant-497', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1070", "duration": 2.07, "duration_str": "2.07s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 214 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-497', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1071", "duration": 2.26, "duration_str": "2.26s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 214 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-497', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1072", "duration": 2.07, "duration_str": "2.07s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 214 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-497', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1073", "duration": 2.15, "duration_str": "2.15s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 215 and `product_attribute_values`.`product_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 215 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-498', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1074", "duration": 2.2, "duration_str": "2.2s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 215 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-498', `description` = '8034412526232-variant-498', `name` = 'Variant 498', `url_key` = '8034412526232-variant-498', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1075", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 215 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-498', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1076", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 215 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-498', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1077", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 215 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-498', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1078", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 211 and `products`.`parent_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.607841, "end": **********.275497, "duration": 0.6676559448242188, "duration_str": "668ms", "measures": [{"label": "Booting", "start": **********.607841, "relative_start": 0, "end": **********.877635, "relative_end": **********.877635, "duration": 0.****************, "duration_str": "270ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.877645, "relative_start": 0.****************, "end": **********.275498, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.891726, "relative_start": 0.*****************, "end": **********.893896, "relative_end": **********.893896, "duration": 0.002170085906982422, "duration_str": "2.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.264445, "relative_start": 0.****************, "end": **********.264779, "relative_end": **********.264779, "duration": 0.00033402442932128906, "duration_str": "334μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 136, "nb_statements": 136, "nb_visible_statements": 136, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.16257000000000005, "accumulated_duration_str": "163ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 36 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.9112961, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 0.271}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.915695, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.271, "width_percent": 0.098}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.922855, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0.369, "width_percent": 0.129}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.924895, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0.498, "width_percent": 0.123}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.926088, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.621, "width_percent": 0.105}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.9302208, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 0.726, "width_percent": 0.129}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.932762, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 0.855, "width_percent": 0.117}, {"sql": "select count(*) as aggregate from `products` where `sku` = '8034412526232'", "type": "query", "params": [], "bindings": ["8034412526232"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.935096, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 0.972, "width_percent": 0.098}, {"sql": "insert into `products` (`type`, `attribute_family_id`, `sku`, `updated_at`, `created_at`) values ('configurable', '2', '8034412526232', '2025-08-03 22:17:55', '2025-08-03 22:17:55')", "type": "query", "params": [], "bindings": ["configurable", "2", "8034412526232", "2025-08-03 22:17:55", "2025-08-03 22:17:55"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 92}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.941206, "duration": 0.01109, "duration_str": "11.09ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 1.07, "width_percent": 6.822}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 211", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 92}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.960309, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 7.892, "width_percent": 0.129}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 211)", "type": "query", "params": [], "bindings": [1, 211], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 92}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.961387, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 8.021, "width_percent": 1.279}, {"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "type": "query", "params": [], "bindings": ["sku", "name", "url_key", "short_description", "description", "price", "weight", "status", "tax_category_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 101}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.964578, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 9.301, "width_percent": 0.16}, {"sql": "select * from `attributes` where `code` = 'color'", "type": "query", "params": [], "bindings": ["color"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 208}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 106}], "start": **********.965733, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 9.461, "width_percent": 0.111}, {"sql": "insert into `product_super_attributes` (`attribute_id`, `product_id`) values (23, 211)", "type": "query", "params": [], "bindings": [23, 211], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 112}, {"index": 12, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 13, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9670148, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "Configurable.php:112", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=112", "ajax": false, "filename": "Configurable.php", "line": "112"}, "connection": "mlk", "explain": null, "start_percent": 9.571, "width_percent": 1.446}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 118}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.970436, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 11.017, "width_percent": 0.098}, {"sql": "insert into `products` (`type`, `sku`, `attribute_family_id`, `parent_id`, `updated_at`, `created_at`) values ('simple', '8034412526232-variant-2', '2', 211, '2025-08-03 22:17:55', '2025-08-03 22:17:55')", "type": "query", "params": [], "bindings": ["simple", "8034412526232-variant-2", "2", 211, "2025-08-03 22:17:55", "2025-08-03 22:17:55"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.971476, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 11.115, "width_percent": 1.267}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 212", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.974349, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 12.382, "width_percent": 0.092}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 212)", "type": "query", "params": [], "bindings": [1, 212], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.975219, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 12.475, "width_percent": 1.218}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 212 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}], "start": **********.978875, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 13.693, "width_percent": 0.111}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (1, null, null, null, null, null, null, null, null, 212, '8034412526232-variant-2', '212|1'), (2, null, null, null, null, null, null, null, 'it', 212, 'Variant 2', 'it|212|2'), (3, null, null, null, null, null, null, null, 'it', 212, '8034412526232-variant-2', 'it|212|3'), (4, null, 'default', null, null, null, '', null, null, 212, null, 'default|212|4'), (8, 1, 'default', null, null, null, null, null, null, 212, null, 'default|212|8'), (9, null, null, null, null, null, null, null, null, 212, '8034412526232-variant-2', '212|9'), (10, null, null, null, null, null, null, null, 'it', 212, '8034412526232-variant-2', 'it|212|10'), (11, null, null, null, null, null, null, null, null, 212, null, '212|11'), (22, null, null, null, null, null, null, null, null, 212, 0, '212|22'), (23, null, null, null, null, null, 2, null, null, 212, null, '212|23')", "type": "query", "params": [], "bindings": [1, null, null, null, null, null, null, null, null, 212, "8034412526232-variant-2", "212|1", 2, null, null, null, null, null, null, null, "it", 212, "Variant 2", "it|212|2", 3, null, null, null, null, null, null, null, "it", 212, "8034412526232-variant-2", "it|212|3", 4, null, "default", null, null, null, "", null, null, 212, null, "default|212|4", 8, 1, "default", null, null, null, null, null, null, 212, null, "default|212|8", 9, null, null, null, null, null, null, null, null, 212, "8034412526232-variant-2", "212|9", 10, null, null, null, null, null, null, null, "it", 212, "8034412526232-variant-2", "it|212|10", 11, null, null, null, null, null, null, null, null, 212, null, "212|11", 22, null, null, null, null, null, null, null, null, 212, 0, "212|22", 23, null, null, null, null, null, 2, null, null, 212, null, "212|23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.9812658, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 13.803, "width_percent": 1.452}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 212 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 221}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.98449, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 15.255, "width_percent": 0.111}, {"sql": "insert into `products` (`type`, `sku`, `attribute_family_id`, `parent_id`, `updated_at`, `created_at`) values ('simple', '8034412526232-variant-496', '2', 211, '2025-08-03 22:17:55', '2025-08-03 22:17:55')", "type": "query", "params": [], "bindings": ["simple", "8034412526232-variant-496", "2", 211, "2025-08-03 22:17:55", "2025-08-03 22:17:55"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.985553, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 15.366, "width_percent": 1.249}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 213", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.9885, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 16.614, "width_percent": 0.105}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 213)", "type": "query", "params": [], "bindings": [1, 213], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.989424, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 16.719, "width_percent": 1.267}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 213 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}], "start": **********.9925778, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 17.986, "width_percent": 0.154}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (1, null, null, null, null, null, null, null, null, 213, '8034412526232-variant-496', '213|1'), (2, null, null, null, null, null, null, null, 'it', 213, 'Variant 496', 'it|213|2'), (3, null, null, null, null, null, null, null, 'it', 213, '8034412526232-variant-496', 'it|213|3'), (4, null, 'default', null, null, null, '', null, null, 213, null, 'default|213|4'), (8, 1, 'default', null, null, null, null, null, null, 213, null, 'default|213|8'), (9, null, null, null, null, null, null, null, null, 213, '8034412526232-variant-496', '213|9'), (10, null, null, null, null, null, null, null, 'it', 213, '8034412526232-variant-496', 'it|213|10'), (11, null, null, null, null, null, null, null, null, 213, null, '213|11'), (22, null, null, null, null, null, null, null, null, 213, 0, '213|22'), (23, null, null, null, null, null, 496, null, null, 213, null, '213|23')", "type": "query", "params": [], "bindings": [1, null, null, null, null, null, null, null, null, 213, "8034412526232-variant-496", "213|1", 2, null, null, null, null, null, null, null, "it", 213, "Variant 496", "it|213|2", 3, null, null, null, null, null, null, null, "it", 213, "8034412526232-variant-496", "it|213|3", 4, null, "default", null, null, null, "", null, null, 213, null, "default|213|4", 8, 1, "default", null, null, null, null, null, null, 213, null, "default|213|8", 9, null, null, null, null, null, null, null, null, 213, "8034412526232-variant-496", "213|9", 10, null, null, null, null, null, null, null, "it", 213, "8034412526232-variant-496", "it|213|10", 11, null, null, null, null, null, null, null, null, 213, null, "213|11", 22, null, null, null, null, null, null, null, null, 213, 0, "213|22", 23, null, null, null, null, null, 496, null, null, 213, null, "213|23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.99504, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 18.14, "width_percent": 1.433}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 213 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 221}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.998261, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 19.573, "width_percent": 0.105}, {"sql": "insert into `products` (`type`, `sku`, `attribute_family_id`, `parent_id`, `updated_at`, `created_at`) values ('simple', '8034412526232-variant-497', '2', 211, '2025-08-03 22:17:55', '2025-08-03 22:17:55')", "type": "query", "params": [], "bindings": ["simple", "8034412526232-variant-497", "2", 211, "2025-08-03 22:17:55", "2025-08-03 22:17:55"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.999269, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 19.678, "width_percent": 1.273}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 214", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.002233, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 20.951, "width_percent": 0.105}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 214)", "type": "query", "params": [], "bindings": [1, 214], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.003277, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 21.056, "width_percent": 1.243}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 214 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}], "start": **********.006323, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 22.298, "width_percent": 0.117}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (1, null, null, null, null, null, null, null, null, 214, '8034412526232-variant-497', '214|1'), (2, null, null, null, null, null, null, null, 'it', 214, 'Variant 497', 'it|214|2'), (3, null, null, null, null, null, null, null, 'it', 214, '8034412526232-variant-497', 'it|214|3'), (4, null, 'default', null, null, null, '', null, null, 214, null, 'default|214|4'), (8, 1, 'default', null, null, null, null, null, null, 214, null, 'default|214|8'), (9, null, null, null, null, null, null, null, null, 214, '8034412526232-variant-497', '214|9'), (10, null, null, null, null, null, null, null, 'it', 214, '8034412526232-variant-497', 'it|214|10'), (11, null, null, null, null, null, null, null, null, 214, null, '214|11'), (22, null, null, null, null, null, null, null, null, 214, 0, '214|22'), (23, null, null, null, null, null, 497, null, null, 214, null, '214|23')", "type": "query", "params": [], "bindings": [1, null, null, null, null, null, null, null, null, 214, "8034412526232-variant-497", "214|1", 2, null, null, null, null, null, null, null, "it", 214, "Variant 497", "it|214|2", 3, null, null, null, null, null, null, null, "it", 214, "8034412526232-variant-497", "it|214|3", 4, null, "default", null, null, null, "", null, null, 214, null, "default|214|4", 8, 1, "default", null, null, null, null, null, null, 214, null, "default|214|8", 9, null, null, null, null, null, null, null, null, 214, "8034412526232-variant-497", "214|9", 10, null, null, null, null, null, null, null, "it", 214, "8034412526232-variant-497", "it|214|10", 11, null, null, null, null, null, null, null, null, 214, null, "214|11", 22, null, null, null, null, null, null, null, null, 214, 0, "214|22", 23, null, null, null, null, null, 497, null, null, 214, null, "214|23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.008857, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 22.415, "width_percent": 1.452}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 214 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 221}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.012026, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 23.867, "width_percent": 0.117}, {"sql": "insert into `products` (`type`, `sku`, `attribute_family_id`, `parent_id`, `updated_at`, `created_at`) values ('simple', '8034412526232-variant-498', '2', 211, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": ["simple", "8034412526232-variant-498", "2", 211, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.0130508, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 23.984, "width_percent": 1.347}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 215", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.0161011, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 25.331, "width_percent": 0.092}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 215)", "type": "query", "params": [], "bindings": [1, 215], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.0169601, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 25.423, "width_percent": 1.261}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 215 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}], "start": **********.020049, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 26.684, "width_percent": 0.105}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (1, null, null, null, null, null, null, null, null, 215, '8034412526232-variant-498', '215|1'), (2, null, null, null, null, null, null, null, 'it', 215, 'Variant 498', 'it|215|2'), (3, null, null, null, null, null, null, null, 'it', 215, '8034412526232-variant-498', 'it|215|3'), (4, null, 'default', null, null, null, '', null, null, 215, null, 'default|215|4'), (8, 1, 'default', null, null, null, null, null, null, 215, null, 'default|215|8'), (9, null, null, null, null, null, null, null, null, 215, '8034412526232-variant-498', '215|9'), (10, null, null, null, null, null, null, null, 'it', 215, '8034412526232-variant-498', 'it|215|10'), (11, null, null, null, null, null, null, null, null, 215, null, '215|11'), (22, null, null, null, null, null, null, null, null, 215, 0, '215|22'), (23, null, null, null, null, null, 498, null, null, 215, null, '215|23')", "type": "query", "params": [], "bindings": [1, null, null, null, null, null, null, null, null, 215, "8034412526232-variant-498", "215|1", 2, null, null, null, null, null, null, null, "it", 215, "Variant 498", "it|215|2", 3, null, null, null, null, null, null, null, "it", 215, "8034412526232-variant-498", "it|215|3", 4, null, "default", null, null, null, "", null, null, 215, null, "default|215|4", 8, 1, "default", null, null, null, null, null, null, 215, null, "default|215|8", 9, null, null, null, null, null, null, null, null, 215, "8034412526232-variant-498", "215|9", 10, null, null, null, null, null, null, null, "it", 215, "8034412526232-variant-498", "it|215|10", 11, null, null, null, null, null, null, null, null, 215, null, "215|11", 22, null, null, null, null, null, null, null, null, 215, 0, "215|22", 23, null, null, null, null, null, 498, null, null, 215, null, "215|23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.0221992, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 26.788, "width_percent": 1.359}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 215 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 221}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.025225, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 28.148, "width_percent": 0.141}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 763}], "start": **********.02861, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Flat.php:60", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=60", "ajax": false, "filename": "Flat.php", "line": "60"}, "connection": "mlk", "explain": null, "start_percent": 28.289, "width_percent": 0.375}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 207}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 132}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}], "start": **********.030069, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 28.665, "width_percent": 0.086}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 207}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 132}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}], "start": **********.0309541, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 28.751, "width_percent": 0.209}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 211", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.032306, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 28.96, "width_percent": 0.111}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 211 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.033673, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 29.071, "width_percent": 0.098}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.035619, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 29.169, "width_percent": 0.129}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [211, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.036899, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 29.298, "width_percent": 0.105}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 34, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.037659, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 29.403, "width_percent": 0.32}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (211, 'default', 'en', 'configurable', '8034412526232', '2', '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [211, "default", "en", "configurable", "8034412526232", "2", "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.038762, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 29.723, "width_percent": 1.353}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1054", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:17:56", 1054], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.043537, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 31.076, "width_percent": 1.347}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": [211, "default", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.046706, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 32.423, "width_percent": 0.129}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (211, 'default', 'it', 'configurable', '8034412526232', '2', '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [211, "default", "it", "configurable", "8034412526232", "2", "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.047612, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 32.552, "width_percent": 1.273}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1055", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:17:56", 1055], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.051551, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 33.825, "width_percent": 1.298}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'de') limit 1", "type": "query", "params": [], "bindings": [211, "default", "de"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.054426, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 35.123, "width_percent": 0.123}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (211, 'default', 'de', 'configurable', '8034412526232', '2', '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [211, "default", "de", "configurable", "8034412526232", "2", "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.055268, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 35.246, "width_percent": 1.261}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1056", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:17:56", 1056], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.059078, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 36.507, "width_percent": 1.323}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'fr') limit 1", "type": "query", "params": [], "bindings": [211, "default", "fr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.062073, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 37.83, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (211, 'default', 'fr', 'configurable', '8034412526232', '2', '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [211, "default", "fr", "configurable", "8034412526232", "2", "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.062918, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 37.947, "width_percent": 1.286}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1057", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:17:56", 1057], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.066824, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 39.232, "width_percent": 1.304}, {"sql": "select * from `product_flat` where (`product_id` = 211 and `channel` = 'default' and `locale` = 'gr') limit 1", "type": "query", "params": [], "bindings": [211, "default", "gr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.069912, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 40.536, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (211, 'default', 'gr', 'configurable', '8034412526232', '2', '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [211, "default", "gr", "configurable", "8034412526232", "2", "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.07073, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 40.647, "width_percent": 1.267}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1058", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:17:56", 1058], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.074664, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 41.914, "width_percent": 1.279}, {"sql": "select * from `products` where `products`.`parent_id` = 211 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [211], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 119}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0779169, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Flat.php:119", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=119", "ajax": false, "filename": "Flat.php", "line": "119"}, "connection": "mlk", "explain": null, "start_percent": 43.194, "width_percent": 0.135}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 212", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.079029, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 43.329, "width_percent": 0.123}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 212 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.079905, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 43.452, "width_percent": 0.111}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.081866, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 43.563, "width_percent": 0.117}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [212, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.0827808, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 43.68, "width_percent": 0.129}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (212, 'default', 'en', 'simple', '8034412526232-variant-2', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [212, "default", "en", "simple", "8034412526232-variant-2", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.083651, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 43.809, "width_percent": 1.249}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1059", "type": "query", "params": [], "bindings": ["8034412526232-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1059], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.088296, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 45.058, "width_percent": 1.304}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": [212, "default", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.091256, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 46.362, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (212, 'default', 'it', 'simple', '8034412526232-variant-2', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [212, "default", "it", "simple", "8034412526232-variant-2", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.092061, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 46.472, "width_percent": 1.255}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = '8034412526232-variant-2', `name` = 'Variant 2', `url_key` = '8034412526232-variant-2', `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1060", "type": "query", "params": [], "bindings": ["8034412526232-variant-2", "8034412526232-variant-2", "Variant 2", "8034412526232-variant-2", null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1060], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.096402, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 47.727, "width_percent": 1.298}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'de') limit 1", "type": "query", "params": [], "bindings": [212, "default", "de"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.099496, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 49.025, "width_percent": 0.129}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (212, 'default', 'de', 'simple', '8034412526232-variant-2', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [212, "default", "de", "simple", "8034412526232-variant-2", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.100382, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 49.154, "width_percent": 1.243}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1061", "type": "query", "params": [], "bindings": ["8034412526232-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1061], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.104783, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 50.397, "width_percent": 1.304}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'fr') limit 1", "type": "query", "params": [], "bindings": [212, "default", "fr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.107864, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 51.701, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (212, 'default', 'fr', 'simple', '8034412526232-variant-2', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [212, "default", "fr", "simple", "8034412526232-variant-2", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.108674, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 51.812, "width_percent": 1.273}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1062", "type": "query", "params": [], "bindings": ["8034412526232-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1062], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.113003, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 53.085, "width_percent": 1.261}, {"sql": "select * from `product_flat` where (`product_id` = 212 and `channel` = 'default' and `locale` = 'gr') limit 1", "type": "query", "params": [], "bindings": [212, "default", "gr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.115992, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 54.346, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (212, 'default', 'gr', 'simple', '8034412526232-variant-2', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [212, "default", "gr", "simple", "8034412526232-variant-2", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.116805, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 54.457, "width_percent": 1.255}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1063", "type": "query", "params": [], "bindings": ["8034412526232-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1063], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.121131, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 55.711, "width_percent": 1.304}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 213", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.124186, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 57.015, "width_percent": 0.129}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 213 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.125069, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 57.145, "width_percent": 0.123}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.1270778, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 57.268, "width_percent": 0.148}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [213, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.128043, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 57.415, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (213, 'default', 'en', 'simple', '8034412526232-variant-496', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [213, "default", "en", "simple", "8034412526232-variant-496", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.1288662, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 57.526, "width_percent": 1.243}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1064", "type": "query", "params": [], "bindings": ["8034412526232-variant-496", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1064], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.133431, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 58.769, "width_percent": 1.409}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": [213, "default", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.136488, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 60.177, "width_percent": 0.105}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (213, 'default', 'it', 'simple', '8034412526232-variant-496', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [213, "default", "it", "simple", "8034412526232-variant-496", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.137395, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 60.282, "width_percent": 1.286}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = '8034412526232-variant-496', `name` = 'Variant 496', `url_key` = '8034412526232-variant-496', `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1065", "type": "query", "params": [], "bindings": ["8034412526232-variant-496", "8034412526232-variant-496", "Variant 496", "8034412526232-variant-496", null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1065], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.142132, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 61.567, "width_percent": 1.255}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'de') limit 1", "type": "query", "params": [], "bindings": [213, "default", "de"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.145113, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 62.822, "width_percent": 0.148}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (213, 'default', 'de', 'simple', '8034412526232-variant-496', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [213, "default", "de", "simple", "8034412526232-variant-496", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.14609, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 62.97, "width_percent": 1.255}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1066", "type": "query", "params": [], "bindings": ["8034412526232-variant-496", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1066], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.15058, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 64.225, "width_percent": 1.31}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'fr') limit 1", "type": "query", "params": [], "bindings": [213, "default", "fr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.153512, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 65.535, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (213, 'default', 'fr', 'simple', '8034412526232-variant-496', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [213, "default", "fr", "simple", "8034412526232-variant-496", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.154326, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 65.646, "width_percent": 1.273}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1067", "type": "query", "params": [], "bindings": ["8034412526232-variant-496", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1067], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1587331, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 66.919, "width_percent": 1.261}, {"sql": "select * from `product_flat` where (`product_id` = 213 and `channel` = 'default' and `locale` = 'gr') limit 1", "type": "query", "params": [], "bindings": [213, "default", "gr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.161521, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 68.18, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (213, 'default', 'gr', 'simple', '8034412526232-variant-496', 2, '2025-08-03 22:17:56', '2025-08-03 22:17:56')", "type": "query", "params": [], "bindings": [213, "default", "gr", "simple", "8034412526232-variant-496", 2, "2025-08-03 22:17:56", "2025-08-03 22:17:56"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.1623292, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 68.291, "width_percent": 1.255}, {"sql": "update `product_flat` set `short_description` = '8034412526232-variant-496', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:17:56' where `id` = 1068", "type": "query", "params": [], "bindings": ["8034412526232-variant-496", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:17:56", 1068], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.166816, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 69.545, "width_percent": 1.279}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 214", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.169905, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 70.825, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 214 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.170824, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 70.966, "width_percent": 0.123}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1727638, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.089, "width_percent": 0.148}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.173261, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.237, "width_percent": 0.092}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1735828, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.329, "width_percent": 1.23}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.177606, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.56, "width_percent": 1.267}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1798658, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.827, "width_percent": 0.129}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.180275, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.956, "width_percent": 1.286}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.184515, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.241, "width_percent": 1.273}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.186759, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.515, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.18714, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.632, "width_percent": 1.304}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1913161, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.936, "width_percent": 1.39}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193785, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 79.326, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194158, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 79.443, "width_percent": 1.249}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.198314, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 80.691, "width_percent": 1.273}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.200573, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 81.965, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2009552, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 82.082, "width_percent": 1.255}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2053208, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 83.336, "width_percent": 1.323}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.207705, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 84.659, "width_percent": 0.135}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.208148, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 84.794, "width_percent": 0.129}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2097988, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 84.923, "width_percent": 0.141}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.210295, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 85.065, "width_percent": 0.105}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.210638, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 85.169, "width_percent": 1.329}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.215008, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 86.498, "width_percent": 1.353}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.217381, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.851, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.217736, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.968, "width_percent": 1.273}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.222184, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.242, "width_percent": 1.298}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.224454, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.539, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.224794, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.65, "width_percent": 1.236}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.229054, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.887, "width_percent": 1.31}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.231387, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.197, "width_percent": 0.105}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2317472, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.301, "width_percent": 1.261}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.235959, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.562, "width_percent": 1.298}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.238267, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.86, "width_percent": 0.129}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.238677, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.989, "width_percent": 1.261}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.242866, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 97.25, "width_percent": 1.31}, {"sql": "select * from `products` where `products`.`parent_id` = ? and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.245176, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.561, "width_percent": 0.111}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.257264, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.671, "width_percent": 1.329}]}, "models": {"data": {"Webkul\\Attribute\\Models\\Attribute": {"value": 42, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductAttributeValue": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}}, "count": 125, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/admin/catalog/products/create", "action_name": "admin.catalog.products.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@store", "uri": "POST admin/catalog/products/create", "controller": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@store<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=87\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/catalog/products", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=87\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php:87-130</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "678ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-667165900 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-667165900\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-496993814 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n  \"<span class=sf-dump-key>attribute_family_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">8034412526232</span>\"\n  \"<span class=sf-dump-key>super_attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>496</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>497</span>\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>498</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496993814\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1382167267 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6IjJ5YkJ2czlJRm9KVEdzS1BTR0pZUVE9PSIsInZhbHVlIjoiT2kva3NJNFRscENJa2Q5eGQzSzZwcXdHaHpKemhZbGVWQjNJK3llVHl1VVFMV2t0blQ2dFpYSENKcGFsa3dOZyIsIm1hYyI6IjUzYjJjMTQ2MDNiOTAwYTA5YTA0ZjcxYmI0MzUxNWNmMjk1Njk1MDA0NjQ2MzkxNDg4MTgzMTNjM2NlYjkzYmIiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImxpZ0pHT2Y5empheEU4LzBaS1hadVE9PSIsInZhbHVlIjoiRjc0N2RhOHV5a01VWk1nSWl1ckUxM014a2JHOTY3SXl2em9DYS85UUlGem9TWjIrZWpFb1p1dUFHNGpJS3lrZGVvRG9IejlMOFdkRDZSNjBlMEs5L2NpRXZlTWZ4RUlaUW5xZEIxR3BSM3FxbnBUbnoxMUxOdmxxM0ozYVlReU0iLCJtYWMiOiI2MDZlNGFkYjkzNjFhYzEyNGQxZDI5ODc4ZTk5MDhjNjUxZThlN2MwYWQzMzk1MmRiNDkwMmU1OTU2YjRlMWIzIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkliS3FFaVhRU3N4QXNDdmhCM0tsUFE9PSIsInZhbHVlIjoiTUR1Z3NyTk9rVS9aVGQ1UjNnYkN2M1BoVFg5SWxIV1ZabVBVbldTMlM5TWFUdG1iZmxhQ0hDbUFkbVlWTm81NGN3WnJxMFFiUkd2dTh0a3U4ek9JNXgveWlRZ2RINlBrbXovREtvR2lxNWtsWnBOcDdSSUFYRUdwejNiS2pJYVciLCJtYWMiOiIyOTczODFmMGRjOTI3OTIxNzI5N2M5NjA0N2RmZmM5ZTNiZjIxZDYwZGY2YzZlNGVmMGUxYTk2Mzc3NTA5NTQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://mlk.test/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImxpZ0pHT2Y5empheEU4LzBaS1hadVE9PSIsInZhbHVlIjoiRjc0N2RhOHV5a01VWk1nSWl1ckUxM014a2JHOTY3SXl2em9DYS85UUlGem9TWjIrZWpFb1p1dUFHNGpJS3lrZGVvRG9IejlMOFdkRDZSNjBlMEs5L2NpRXZlTWZ4RUlaUW5xZEIxR3BSM3FxbnBUbnoxMUxOdmxxM0ozYVlReU0iLCJtYWMiOiI2MDZlNGFkYjkzNjFhYzEyNGQxZDI5ODc4ZTk5MDhjNjUxZThlN2MwYWQzMzk1MmRiNDkwMmU1OTU2YjRlMWIzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">116</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382167267\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2050339123 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jxmpOQ2BAfhFaqxfLAYWsgCe5beWeDeiyQktko9E</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JEicZFJies5owWh90sx51Kroy2fKDnW4PkLRo5Wg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050339123\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-835201484 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 03 Aug 2025 21:17:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835201484\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-282542512 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jxmpOQ2BAfhFaqxfLAYWsgCe5beWeDeiyQktko9E</span>\"\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://mlk.test/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#20135;&#21697;&#21019;&#24314;&#25104;&#21151;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-282542512\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/admin/catalog/products/create", "action_name": "admin.catalog.products.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@store"}, "badge": null}}