<?php

namespace Webkul\MLKWebAPI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Product\Helpers\Review;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Customer\Repositories\WishlistRepository;

class ProductResource extends JsonResource
{
    /**
     * Review helper instance
     *
     * @var \Webkul\Product\Helpers\Review
     */
    protected $reviewHelper;

    /**
     * Customer repository instance
     *
     * @var \Webkul\Customer\Repositories\CustomerRepository
     */
    protected $customerRepository;

    /**
     * Wishlist repository instance
     *
     * @var \Webkul\Customer\Repositories\WishlistRepository
     */
    protected $wishlistRepository;

    public function __construct($resource)
    {
        $this->reviewHelper = app(Review::class);
        $this->customerRepository = app(CustomerRepository::class);
        $this->wishlistRepository = app(WishlistRepository::class);

        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if (!$this->resource) {
            return [];
        }

        $product = $this->resource;
        $productTypeInstance = $this->getTypeInstance();
        // 获取折扣信息
        $discountInfo = $this->getProductDiscountInfo($product, $productTypeInstance);
        return [
            'id'                 => $product->id,
            'type'               => $product->type,
            'name'               => $product->name,
            'url_key'            => $product->url_key,
            'sku'                => $product->sku,
            'product_number'     => $product->product_number,
            'new'                => $product->new,
            'featured'           => $product->featured,
            'visible_individually' => $product->visible_individually,
            'status'             => $product->status,
            
            // 价格相关字段
            'price'              => $discountInfo['final_price'],
            'formatted_price'    => core()->currency($discountInfo['final_price']),
            'base_price'         => $discountInfo['regular_price'],
            'formatted_base_price' => core()->currency($discountInfo['regular_price']),
            'regular_price'      => $discountInfo['regular_price'],
            'formatted_regular_price' => core()->currency($discountInfo['regular_price']),
            'special_price'      => $product->special_price,
            'formatted_special_price' => $product->special_price ? core()->currency($product->special_price) : null,
            'min_price'          => core()->formatPrice($productTypeInstance->getMinimalPrice()),
            'max_price'          => $product->max_price,
            'prices'             => $productTypeInstance->getProductPrices(),

            // 描述信息
            'short_description'  => strip_tags($product->short_description),
            'description'        => $product->description,
            
            // 图片相关字段
            'base_image'         => $product->base_image_url,
            // 'small_image'        => $product->small_image_url,
            // 'thumbnail'          => $product->thumbnail_url,
            'images'             => $this->getProductImages($product),
            'images_gallery'     => $this->getOptimizedGalleryImages($product),
            'base_image_gallery' => $this->getOptimizedBaseImage($product),
            'videos'             => $product->videos->map(function ($video) {
                return [
                    'id' => $video->id,
                    'url' => $video->url,
                    'type' => $video->type,
                ];
            }),
            
            // 库存相关
            'in_stock'           => $this->getProductStockStatus($product),
            'show_quantity_changer' => $product->show_quantity_changer,
            
            // 状态标识
            'on_sale'            => (bool) $productTypeInstance->haveDiscount(),
            'is_saleable'        => (bool) $productTypeInstance->isSaleable(),
            'is_new'             => (bool) $this->new,
            'is_featured'        => (bool) $this->featured,
            'is_in_wishlist'     => (bool) $this->getUserWishlistStatus(),

            // 分类信息
            'categories'         => $this->getProductCategories($product),
            
            // 属性信息
            'attributes'         => $this->getProductAttributes($product),
            'attribute_family'   => $product->attribute_family ? [
                'id' => $product->attribute_family->id,
                'name' => $product->attribute_family->name,
                'code' => $product->attribute_family->code,
            ] : null,

            // // 评价信息
            // 'reviews_count'      => $product->reviews->count(),
            // 'reviews_avg_rating' => $product->reviews->avg('rating') ?? 0,
            
            // // 时间戳
            // 'created_at'         => $product->created_at,
            // 'updated_at'         => $product->updated_at,
            
            // 扩展字段
            'color'              => $this->getProductColorValue($product),
            'discount'           => $this->formatDiscountData($discountInfo),
            
            // 可配置产品专用字段
            'variants'           => $product->type == 'configurable' ? $this->getProductVariants($product) : [],
            'super_attributes'   => $product->type == 'configurable' ? $this->getSuperAttributes($product) : [],
        ];
    }

    /**
     * 获取产品的折扣信息
     *
     * @param \Webkul\Product\Models\Product $product
     * @param mixed $typeInstance
     * @return array
     */
    protected function getProductDiscountInfo($product, $typeInstance)
    {
        try {
            // 获取原价和最终价格
            $regularPrice = (float) ($product->price ?: 0);
            $finalPrice = (float) $typeInstance->getFinalPrice();
            
            // 确保价格为正数
            $regularPrice = max(0, $regularPrice);
            $finalPrice = max(0, $finalPrice);
            
            // 计算折扣金额和百分比
            $discountAmount = max(0, $regularPrice - $finalPrice);
            $discountPercentage = $regularPrice > 0 ? round(($discountAmount / $regularPrice) * 100, 0) : 0;
            
            // 检查特价信息
            $specialPrice = $product->special_price ? (float) $product->special_price : null;
            $specialPriceFrom = $product->special_price_from;
            $specialPriceTo = $product->special_price_to;
            
            // 检查特价是否在有效期内
            $isSpecialPriceActive = $specialPrice && core()->isChannelDateInInterval($specialPriceFrom, $specialPriceTo);
            
            // 检查是否有折扣
            $hasDiscount = $discountAmount > 0 || $typeInstance->haveDiscount();
            
            return [
                'has_discount' => $hasDiscount,
                'regular_price' => $regularPrice,
                'final_price' => $finalPrice,
                'discount_amount' => $discountAmount,
                'discount_percentage' => $discountPercentage,
                'special_price' => $specialPrice,
                'special_price_from' => $specialPriceFrom,
                'special_price_to' => $specialPriceTo,
                'is_special_price_active' => $isSpecialPriceActive,
            ];
        } catch (\Exception $e) {
            // 如果获取折扣信息失败，返回基本信息
            $regularPrice = (float) ($product->price ?: 0);
            
            return [
                'has_discount' => false,
                'regular_price' => $regularPrice,
                'final_price' => $regularPrice,
                'discount_amount' => 0,
                'discount_percentage' => 0,
                'special_price' => null,
                'special_price_from' => null,
                'special_price_to' => null,
                'is_special_price_active' => false,
            ];
        }
    }

    /**
     * 格式化折扣数据
     *
     * @param array $discountInfo
     * @return array
     */
    protected function formatDiscountData($discountInfo)
    {
        return [
            'has_discount' => $discountInfo['has_discount'],
            'regular_price' => $discountInfo['regular_price'],
            'formatted_regular_price' => core()->currency($discountInfo['regular_price']),
            'final_price' => $discountInfo['final_price'],
            'formatted_final_price' => core()->currency($discountInfo['final_price']),
            'discount_amount' => $discountInfo['discount_amount'],
            'formatted_discount_amount' => core()->currency($discountInfo['discount_amount']),
            'discount_percentage' => $discountInfo['discount_percentage'],
            'special_price' => $discountInfo['special_price'],
            'formatted_special_price' => $discountInfo['special_price'] ? core()->currency($discountInfo['special_price']) : null,
            'special_price_from' => $discountInfo['special_price_from'],
            'special_price_to' => $discountInfo['special_price_to'],
            'is_special_price_active' => $discountInfo['is_special_price_active'],
        ];
    }

    /**
     * 获取用户wishlist状态
     *
     * @return bool
     */
    protected function getUserWishlistStatus()
    {
        // 优先使用预加载的wishlist状态
        if ($this->relationLoaded('user_wishlist_status')) {
            return $this->getRelation('user_wishlist_status');
        }
        
        // 如果没有预加载数据，检查用户是否已登录
        $user = auth()->guard('sanctum')->user();
        if (!$user) {
            return false;
        }
        
        // 作为备选方案，使用wishlist repository查询（这应该很少触发）
        try {
            $wishlistRepository = app('Webkul\Customer\Repositories\WishlistRepository');
            $wishlistItem = $wishlistRepository->findWhere([
                'customer_id' => $user->id,
                'channel_id' => core()->getCurrentChannel()->id,
                'product_id' => $this->id,
            ])->first();
            
            return $wishlistItem !== null;
        } catch (\Exception $e) {
            logger()->warning('Failed to check wishlist status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取优化的产品库存状态（避免N+1查询）
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return bool
     */
    protected function getProductStockStatus($product)
    {
        try {
            // 如果预加载了inventory_indices，使用预加载的数据
            if ($product->relationLoaded('inventory_indices') && $product->inventory_indices->isNotEmpty()) {
                $totalQty = $product->inventory_indices->sum('qty');
                return $totalQty >= 1;
            }
            
            // 否则使用产品的默认方法（这应该很少发生）
            return $product->haveSufficientQuantity(1);
        } catch (\Exception $e) {
            logger()->warning('Failed to check product stock status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取优化的图片画廊（避免N+1查询）
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getOptimizedGalleryImages($product)
    {
        try {
            // 使用预加载的images关系，避免额外查询
            return $product->images->map(function ($image) {
                return [
                    'id' => $image->id,
                    'url' => $image->url,
                    'path' => $image->path,
                    'original_image_url' => $image->url,
                    'medium_image_url' => $image->url,
                    'small_image_url' => $image->url,
                ];
            })->toArray();
        } catch (\Exception $e) {
            logger()->warning('Failed to get gallery images: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取优化的基础图片（避免N+1查询）
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array|null
     */
    protected function getOptimizedBaseImage($product)
    {
        try {
            // 使用预加载的images关系获取第一张图片作为基础图片
            $baseImage = $product->images->first();
            
            if (!$baseImage) {
                return [
                    'original_image_url' => null,
                    'medium_image_url' => null,
                    'small_image_url' => null,
                ];
            }
            
            return [
                'original_image_url' => $baseImage->url,
                'medium_image_url' => $baseImage->url,
                'small_image_url' => $baseImage->url,
            ];
        } catch (\Exception $e) {
            logger()->warning('Failed to get base image: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取产品图片集合
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getProductImages($product)
    {
        $images = [];
        
        foreach ($product->images as $image) {
            $images[] = [
                'id'   => $image->id,
                'path' => $image->path,
                'url'  => $image->url,
            ];
        }
        
        return $images;
    }

    /**
     * 获取产品分类信息
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getProductCategories($product)
    {
        $categories = [];
        
        foreach ($product->categories as $category) {
            $categories[] = [
                'id'   => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'url'  => $category->url,
            ];
        }
        
        return $categories;
    }

    /**
     * 获取产品属性信息（优化版本，避免N+1查询）
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getProductAttributes($product)
    {
        $attributes = [];
        
        // 使用预加载的attribute_values和attribute关系
        if (!$product->relationLoaded('attribute_values')) {
            return $attributes;
        }
        
        $attributeValues = $product->attribute_values->groupBy('attribute_id');
        foreach ($attributeValues as $attributeValueGroup) {
            $attributeValue = $attributeValueGroup->first();
            $attribute = $attributeValue->attribute;
            
            if ($attribute && $attribute->is_visible_on_front) {
                $attributes[] = [
                    'id'        => $attribute->id,
                    'code'      => $attribute->code,
                    'name'      => $attribute->name,
                    'type'      => $attribute->type,
                    'value'     => $attributeValue->value ?? null,
                    'label'     => $attributeValue->label ?? null,
                    'is_filterable' => $attribute->is_filterable,
                ];
            }
        }
        return $attributes;
    }

    /**
     * 获取产品的color属性值
     *
     * @param \Webkul\Product\Models\Product $product
     * @return array|null
     */
    protected function getProductColorValue($product)
    {
        try {
            // 对于configurable产品，获取所有变体的color值
            if ($product->type === 'configurable') {
                return $this->getConfigurableProductColors($product);
            } else {
                // 对于simple产品，直接获取color属性值,统一返回数组
                return $this->getSimpleProductColor($product);
            }
        } catch (\Exception $e) {
            // 如果获取color失败，返回null
            return null;
        }
    }

    /**
     * 获取configurable产品的所有color选项
     *
     * @param \Webkul\Product\Models\Product $product
     * @return array|null
     */
    protected function getConfigurableProductColors($product)
    {
        $colors = [];
        
        // 检查是否有color超级属性
        $colorAttribute = $product->super_attributes->where('code', 'color')->first();
        
        if (!$colorAttribute) {
            return null;
        }
        
        // 使用预加载的变体产品数据，避免额外查询
        $variants = $product->variants;
        
        // 使用预加载的options关系，避免N+1查询
        $colorOptions = [];
        if ($colorAttribute->relationLoaded('options')) {
            $colorOptions = $colorAttribute->options->keyBy('id');
        }
        
        foreach ($variants as $variant) {
            $colorValue = $variant->color; // 使用Product模型的getAttribute魔术方法
            
            if ($colorValue && isset($colorOptions[$colorValue])) {
                $colorOption = $colorOptions[$colorValue];
                
                $colorData = [
                    'option_id' => $colorOption->id,
                    'option_value' => $colorValue,
                    'option_label' => $colorOption->admin_name,
                    'variant_id' => $variant->id,
                    'swatch_value' => $colorOption->swatch_value,
                ];
                
                // 避免重复的color选项
                $exists = false;
                foreach ($colors as $existingColor) {
                    if ($existingColor['option_id'] == $colorData['option_id']) {
                        $exists = true;
                        break;
                    }
                }
                
                if (!$exists) {
                    $colors[] = $colorData;
                }
            }
        }
        
        return empty($colors) ? null : $colors;
    }

    /**
     * 获取simple产品的color值
     *
     * @param \Webkul\Product\Models\Product $product
     * @return array|null
     */
    protected function getSimpleProductColor($product)
    {
        $colorValue = $product->color; // 使用Product模型的getAttribute魔术方法
        if (!$colorValue) {
            return null;
        }
        
        // 尝试从产品的属性值中获取color属性信息
        $colorAttribute = null;
        $colorOption = null;
        
        if ($product->relationLoaded('attribute_values')) {
            $colorAttributeValue = $product->attribute_values->where('attribute.code', 'color')->first();
            if ($colorAttributeValue && $colorAttributeValue->relationLoaded('attribute')) {
                $colorAttribute = $colorAttributeValue->attribute;
                
                // 如果是select类型属性且预加载了options，获取选项标签
                if ($colorAttribute->type === 'select' && $colorAttribute->relationLoaded('options')) {
                    $colorOption = $colorAttribute->options->where('id', $colorValue)->first();
                }
            }
        }
        
        // 如果没有找到预加载的数据，回退到直接查询
        if (!$colorAttribute) {
            $attributeRepository = app('Webkul\Attribute\Repositories\AttributeRepository');
            $colorAttribute = $attributeRepository->findOneByField('code', 'color');
            
            if (!$colorAttribute) {
                return [[
                    'option_value' => $colorValue,
                    'option_label' => $colorValue,
                    'variant_id' => $product->id,
                ]];
            }
            
            // 如果是select类型属性，获取选项标签
            if ($colorAttribute->type === 'select') {
                $colorOption = $colorAttribute->options()->where('id', $colorValue)->first();
            }
        }
        
        if ($colorOption) {
            return [[
                'option_id' => $colorOption->id,
                'option_value' => $colorValue,
                'option_label' => $colorOption->admin_name,
                'variant_id' => $product->id,
            ]];
        }
        
        // 对于其他类型的属性，直接返回值
        return [[
            'option_value' => $colorValue,
            'option_label' => $colorValue,
            'variant_id' => $product->id,
        ]];
    }

    /**
     * 获取产品变体信息
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getProductVariants($product)
    {
        $variants = [];
        
        foreach ($product->variants as $variant) {
            $variants[] = [
                'id'    => $variant->id,
                'sku'   => $variant->sku,
                'name'  => $variant->name,
                'price' => $variant->price,
                'formatted_price' => core()->currency($variant->price),
                'in_stock' => $variant->haveSufficientQuantity(1),
            ];
        }
        
        return $variants;
    }

    /**
     * 获取可配置属性信息（优化版本，避免N+1查询）
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getSuperAttributes($product)
    {
        $superAttributes = [];
        
        if ($product->type != 'configurable') {
            return $superAttributes;
        }
        
        // 使用预加载的super_attributes.options.translations关系
        if (!$product->relationLoaded('super_attributes')) {
            return $superAttributes;
        }
        
        foreach ($product->super_attributes as $attribute) {
            $options = [];
            
            // 使用预加载的options关系
            if ($attribute->relationLoaded('options')) {
                foreach ($attribute->options as $option) {
                    $options[] = [
                        'id'    => $option->id,
                        'label' => $option->label,
                        'value' => $option->id,
                        'swatch_value' => $option->swatch_value,
                    ];
                }
            }
            
            $superAttributes[] = [
                'id'      => $attribute->id,
                'code'    => $attribute->code,
                'name'    => $attribute->name,
                'type'    => $attribute->type,
                'options' => $options,
            ];
        }
        
        return $superAttributes;
    }

    // /**
    //  * 检查产品是否在当前用户的wishlist中
    //  *
    //  * @param int $productId
    //  * @return bool
    //  */
    // protected function isProductInWishlist($productId)
    // {
    //     // 检查用户是否已登录
    //     $user = auth()->guard('sanctum')->user();
    //     if (!$user) {
    //         return false;
    //     }

    //     try {
    //         // 获取当前用户在当前渠道的wishlist产品ID
    //         $wishlistItems = $this->wishlistRepository->findWhere([
    //             'customer_id' => $user->id,
    //             'channel_id' => core()->getCurrentChannel()->id,
    //             'product_id' => $productId,
    //         ]);
            
    //         return $wishlistItems->count() > 0;
    //     } catch (\Exception $e) {
    //         // 如果获取失败，记录错误但继续执行
    //         logger()->warning('Failed to check product in wishlist: ' . $e->getMessage());
    //         return false;
    //     }
    // }
} 