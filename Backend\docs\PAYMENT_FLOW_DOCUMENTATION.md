# Bagisto支付流程详细说明

## 概述

本文档详细说明了Bagisto电商系统中用户下单后的完整支付流程，包括不同支付方式的处理机制、订单状态管理和支付确认流程。

## 支付流程总览

```mermaid
graph TD
    A[用户提交订单] --> B{检查支付方式}
    B -->|需要重定向| C[跳转到支付网关]
    B -->|无需重定向| D[直接创建订单]
    C --> E[用户在支付网关完成支付]
    E --> F[支付网关回调]
    F --> G[验证支付结果]
    G -->|成功| H[更新订单状态]
    G -->|失败| I[标记支付失败]
    D --> J[订单创建成功]
    H --> K[生成发票]
    J --> L[等待支付确认]
    K --> M[订单完成]
```

## 1. 订单创建阶段

### 1.1 订单提交
当用户点击"提交订单"时，系统执行以下步骤：

```php
// CheckoutController::storeOrder()
public function storeOrder(): JsonResponse
{
    // 1. 验证购物车和用户信息
    Cart::collectTotals();
    $this->validateOrder();
    
    // 2. 检查是否需要支付重定向
    $cart = Cart::getCart();
    if ($redirectUrl = Payment::getRedirectUrl($cart)) {
        return $this->success([
            'redirect' => true,
            'redirect_url' => $redirectUrl,
        ]);
    }
    
    // 3. 创建订单
    $data = (new OrderResource($cart))->jsonSerialize();
    $order = $this->orderRepository->create($data);
    
    // 4. 停用购物车
    Cart::deActivateCart();
    
    return $this->success(['order' => $order]);
}
```

### 1.2 订单初始状态
新创建的订单初始状态为 `pending`：

```php
// OrderRepository::create()
$data['status'] = Order::STATUS_PENDING; // 'pending'
$order = $this->model->create($data);
```

## 2. 支付方式分类

### 2.1 无需重定向的支付方式

#### 货到付款 (Cash on Delivery)
- **代码**: `cashondelivery`
- **特点**: 无需在线支付，订单直接创建
- **流程**: 订单创建 → 等待发货 → 收货时付款

```php
// CashOnDelivery::getRedirectUrl()
public function getRedirectUrl() {
    return null; // 无需重定向
}
```

#### 银行转账 (Money Transfer)
- **代码**: `moneytransfer`
- **特点**: 线下转账，需要管理员手动确认
- **流程**: 订单创建 → 用户转账 → 管理员确认 → 订单处理

### 2.2 需要重定向的支付方式

#### PayPal Standard
- **代码**: `paypal_standard`
- **特点**: 重定向到PayPal网站完成支付
- **流程**: 订单创建 → 重定向到PayPal → 支付完成 → 回调处理

#### PayPal Smart Button
- **代码**: `paypal_smart_button`
- **特点**: 在页面内嵌入PayPal支付按钮
- **流程**: 前端调用PayPal API → 支付确认 → 后端处理

## 3. 支付确认流程

### 3.1 PayPal支付确认

#### PayPal Standard回调处理
```php
// StandardController::success()
public function success()
{
    $cart = Cart::getCart();
    $data = (new OrderResource($cart))->jsonSerialize();
    $order = $this->orderRepository->create($data);
    
    Cart::deActivateCart();
    session()->flash('order_id', $order->id);
    
    return redirect()->route('shop.checkout.onepage.success');
}
```

#### PayPal IPN (Instant Payment Notification) 处理
```php
// Ipn::processOrder()
protected function processOrder()
{
    if ($this->post['payment_status'] == 'Completed') {
        // 验证支付金额
        if ($this->post['mc_gross'] != $this->order->grand_total) {
            return;
        }
        
        // 更新订单状态为处理中
        $this->orderRepository->update(['status' => 'processing'], $this->order->id);
        
        // 创建发票
        if ($this->order->canInvoice()) {
            $invoice = $this->invoiceRepository->create($this->prepareInvoiceData());
        }
    }
}
```

### 3.2 Smart Button支付确认
```php
// SmartButtonController::captureOrder()
public function captureOrder()
{
    // 1. 验证PayPal订单
    // 2. 创建系统订单
    $order = $this->orderRepository->create($data);
    
    // 3. 更新订单状态
    $this->orderRepository->update(['status' => 'processing'], $order->id);
    
    // 4. 创建发票
    if ($order->canInvoice()) {
        $this->invoiceRepository->create($this->prepareInvoiceData($order));
    }
    
    Cart::deActivateCart();
}
```

## 4. 订单状态生命周期

### 4.1 订单状态定义
```php
// Order Model 状态常量
const STATUS_PENDING = 'pending';           // 待处理
const STATUS_PENDING_PAYMENT = 'pending_payment'; // 待支付
const STATUS_PROCESSING = 'processing';     // 处理中
const STATUS_COMPLETED = 'completed';       // 已完成
const STATUS_CANCELED = 'canceled';         // 已取消
const STATUS_CLOSED = 'closed';            // 已关闭
const STATUS_FRAUD = 'fraud';              // 欺诈
```

### 4.2 状态转换规则

#### 自动状态更新
```php
// OrderRepository::updateOrderStatus()
public function updateOrderStatus($order, $orderState = null)
{
    if (!empty($orderState)) {
        $status = $orderState;
    } else {
        $status = Order::STATUS_PROCESSING;
        
        if ($this->isInCompletedState($order)) {
            $status = Order::STATUS_COMPLETED;
        }
        
        if ($this->isInCanceledState($order)) {
            $status = Order::STATUS_CANCELED;
        } elseif ($this->isInClosedState($order)) {
            $status = Order::STATUS_CLOSED;
        }
    }
    
    $order->status = $status;
    $order->save();
}
```

#### 完成状态判断条件
订单被标记为"已完成"需要满足：
1. 所有商品都已开票
2. 所有实物商品都已发货
3. 所有虚拟商品都已交付

```php
// OrderRepository::isInCompletedState()
public function isInCompletedState($order)
{
    // 计算各种数量
    $totalQtyOrdered = $totalQtyInvoiced = $totalQtyShipped = 0;
    
    foreach ($order->items as $item) {
        $totalQtyOrdered += $item->qty_ordered;
        $totalQtyInvoiced += $item->qty_invoiced;
        
        if (!$item->isStockable()) {
            $totalQtyShipped += $item->qty_invoiced; // 虚拟商品
        } else {
            $totalQtyShipped += $item->qty_shipped;  // 实物商品
        }
    }
    
    // 判断是否完成
    return ($totalQtyOrdered === $totalQtyInvoiced + $totalQtyCanceled) &&
           ($totalQtyInvoiced === $totalQtyShipped);
}
```

## 5. 发票生成机制

### 5.1 自动发票生成
某些支付方式支持自动生成发票：

```php
// GenerateInvoice Listener
public function handle($order)
{
    // 货到付款自动生成发票
    if ($order->payment->method == 'cashondelivery' &&
        core()->getConfigData('sales.payment_methods.cashondelivery.generate_invoice')) {
        
        $this->invoiceRepository->create(
            $this->prepareInvoiceData($order),
            core()->getConfigData('sales.payment_methods.cashondelivery.invoice_status'),
            core()->getConfigData('sales.payment_methods.cashondelivery.order_status')
        );
    }
    
    // 银行转账自动生成发票
    if ($order->payment->method == 'moneytransfer' &&
        core()->getConfigData('sales.payment_methods.moneytransfer.generate_invoice')) {
        
        $this->invoiceRepository->create(
            $this->prepareInvoiceData($order),
            core()->getConfigData('sales.payment_methods.moneytransfer.invoice_status'),
            core()->getConfigData('sales.payment_methods.moneytransfer.order_status')
        );
    }
}
```

### 5.2 发票状态
- `pending` - 待处理
- `paid` - 已支付
- `pending_payment` - 待支付

## 6. 支付事务记录

### 6.1 事务创建
支付成功后会创建支付事务记录：

```php
// Transaction Listener (PayPal)
$this->orderTransactionRepository->create([
    'transaction_id' => $transactionDetails['result']['id'],
    'status'         => $transactionDetails['result']['status'],
    'type'           => $transactionDetails['result']['intent'],
    'amount'         => $transactionDetails['result']['purchase_units'][0]['amount']['value'],
    'payment_method' => $invoice->order->payment->method,
    'order_id'       => $invoice->order->id,
    'invoice_id'     => $invoice->id,
]);
```

### 6.2 支付确认
当事务总额达到发票金额时，自动确认支付：

```php
// TransactionController::store()
$transactionTotal = $this->orderTransactionRepository
    ->where('invoice_id', $invoice->id)
    ->sum('amount');

if ($transactionTotal >= $invoice->base_grand_total) {
    $shipments = $this->shipmentRepository->where('order_id', $invoice->order_id)->first();
    
    $status = isset($shipments) 
        ? Order::STATUS_COMPLETED 
        : Order::STATUS_PROCESSING;
    
    $this->orderRepository->updateOrderStatus($order, $status);
    $this->invoiceRepository->updateState($invoice, Invoice::STATUS_PAID);
}
```

## 7. 不同支付方式的具体流程

### 7.1 货到付款流程
1. 用户选择货到付款
2. 提交订单，状态为 `pending`
3. 系统自动生成发票（如果配置启用）
4. 商家准备发货
5. 发货后状态变为 `processing`
6. 用户收货并付款
7. 管理员确认收款，状态变为 `completed`

### 7.2 PayPal支付流程
1. 用户选择PayPal支付
2. 提交订单，重定向到PayPal
3. 用户在PayPal完成支付
4. PayPal回调成功页面
5. 系统创建订单，状态为 `processing`
6. 自动生成已支付发票
7. 商家发货后状态变为 `completed`

### 7.3 银行转账流程
1. 用户选择银行转账
2. 提交订单，状态为 `pending`
3. 系统显示转账信息
4. 用户完成线下转账
5. 管理员手动确认收款
6. 创建支付事务记录
7. 订单状态更新为 `processing`
8. 后续发货和完成流程

## 8. 错误处理和异常情况

### 8.1 支付失败处理
- PayPal支付取消：重定向回购物车
- 支付金额不匹配：不更新订单状态
- 网络异常：重试机制或手动处理

### 8.2 订单取消
用户或管理员可以取消订单：
```php
// OrderRepository::cancel()
public function cancel($orderOrId)
{
    $order = $this->resolveOrderInstance($orderOrId);
    
    if (!$order->canCancel()) {
        return false;
    }
    
    // 恢复库存
    foreach ($order->items as $item) {
        $this->orderItemRepository->returnQtyToProductInventory($item);
        $item->qty_canceled += $item->qty_to_cancel;
        $item->save();
    }
    
    $this->updateOrderStatus($order);
    return true;
}
```

## 9. 配置和自定义

### 9.1 支付方式配置
管理员可以在后台配置各种支付方式的参数：
- 是否启用
- 自动生成发票
- 默认订单状态
- 发票状态

### 9.2 扩展支付方式
开发者可以通过实现 `Payment` 抽象类来添加新的支付方式：

```php
class CustomPayment extends Payment
{
    protected $code = 'custom_payment';
    
    public function getRedirectUrl() {
        // 返回支付网关URL或null
    }
    
    public function isAvailable() {
        // 检查支付方式是否可用
    }
}
```

## 10. 支付方式对比表

| 特性 | 货到付款 | 银行转账 | PayPal Standard | PayPal Smart Button |
|------|---------|---------|----------------|-------------------|
| **支付时机** | 收货时 | 下单后线下 | 下单时在线 | 下单时在线 |
| **是否重定向** | 否 | 否 | 是 | 否 |
| **订单初始状态** | pending | pending | pending → processing | pending → processing |
| **发票生成** | 可配置自动 | 可配置自动 | 支付成功后 | 支付成功后 |
| **支付确认** | 管理员手动 | 管理员手动 | 自动 | 自动 |
| **库存扣减** | 下单时 | 下单时 | 下单时 | 下单时 |
| **退款支持** | 手动 | 手动 | 支持 | 支持 |
| **适用场景** | 本地配送 | 企业采购 | 国际支付 | 快速支付 |

## 11. API接口支付流程示例

### 11.1 货到付款完整流程
```bash
# 1. 选择支付方式
POST /api/mlk/checkout/payment-methods
{
    "payment": {
        "method": "cashondelivery"
    }
}

# 2. 提交订单
POST /api/mlk/checkout/orders
# 响应：
{
    "success": true,
    "data": {
        "order": {
            "id": 1001,
            "status": "pending",
            "payment_method": "cashondelivery"
        }
    }
}

# 3. 管理员发货（后台操作）
# 4. 用户收货付款
# 5. 管理员确认收款，订单完成
```

### 11.2 PayPal支付完整流程
```bash
# 1. 选择PayPal支付
POST /api/mlk/checkout/payment-methods
{
    "payment": {
        "method": "paypal_standard"
    }
}

# 2. 提交订单
POST /api/mlk/checkout/orders
# 响应：
{
    "success": true,
    "data": {
        "redirect": true,
        "redirect_url": "https://www.paypal.com/cgi-bin/webscr?..."
    }
}

# 3. 前端重定向到PayPal
# 4. 用户完成支付
# 5. PayPal回调处理，订单自动变为processing状态
```

## 12. 监控和日志

### 12.1 支付事件监听
系统通过事件系统记录支付过程中的关键节点：

```php
// 订单创建前后
Event::dispatch('checkout.order.save.before', [$data]);
Event::dispatch('checkout.order.save.after', $order);

// 订单状态更新前后
Event::dispatch('sales.order.update-status.before', $order);
Event::dispatch('sales.order.update-status.after', $order);

// 发票创建前后
Event::dispatch('sales.invoice.save.before', $data);
Event::dispatch('sales.invoice.save.after', $invoice);
```

### 12.2 支付日志记录
建议在关键支付节点添加日志记录：

```php
// 支付回调处理
Log::info('Payment callback received', [
    'payment_method' => $paymentMethod,
    'order_id' => $orderId,
    'amount' => $amount,
    'status' => $status
]);

// 订单状态变更
Log::info('Order status updated', [
    'order_id' => $order->id,
    'old_status' => $oldStatus,
    'new_status' => $newStatus
]);
```

## 13. 安全考虑

### 13.1 支付验证
- 验证支付金额与订单金额一致
- 验证支付来源的合法性
- 防止重复支付处理
- 签名验证（如PayPal IPN）

### 13.2 订单安全
- 用户只能操作自己的订单
- 订单状态变更权限控制
- 敏感信息加密存储
- 支付信息不存储敏感数据

## 14. 性能优化

### 14.1 异步处理
对于耗时的支付处理操作，建议使用队列异步处理：

```php
// 支付回调处理
dispatch(new ProcessPaymentCallback($paymentData));

// 发票生成
dispatch(new GenerateInvoice($order));
```

### 14.2 缓存策略
- 支付方式配置缓存
- 订单状态缓存
- 支付网关响应缓存

## 总结

Bagisto的支付流程设计灵活且可扩展，支持多种支付方式和业务场景。通过事件系统和监听器，可以轻松扩展支付功能和自定义业务逻辑。订单状态管理自动化程度高，减少了手动操作的需要，同时保持了足够的灵活性来处理各种特殊情况。

关键特点：
- **多支付方式支持**：货到付款、银行转账、PayPal等
- **自动状态管理**：基于业务规则自动更新订单状态
- **灵活的发票系统**：支持自动和手动发票生成
- **完善的事件系统**：便于扩展和自定义
- **安全的支付处理**：多重验证确保支付安全
- **良好的错误处理**：完善的异常处理机制
