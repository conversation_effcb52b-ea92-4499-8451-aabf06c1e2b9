# 用户地址管理 API 接口文档

## 概述

本文档描述了MLK Web API中用户地址管理相关的接口功能，包括地址的增删改查操作。

**文档特色**：
- 📝 **完整CRUD操作**: 支持地址的创建、读取、更新、删除
- 📊 **详细字段说明**: 每个字段都有完整的类型和说明
- 🏠 **默认地址管理**: 支持设置和管理默认收货地址
- 🔧 **严格验证**: 完整的输入参数验证和错误处理

## 🔐 认证要求

所有地址管理接口都需要用户认证，需在请求头中携带Bearer Token：

```
Authorization: Bearer {access_token}
```

## 📋 接口列表

### 1. 获取地址列表

**接口地址**: `GET /api/mlk/addresses`

**接口描述**: 获取当前登录用户的所有地址列表，默认地址排在前面

#### 📥 请求参数

无需参数

#### 📤 响应字段说明

**基础响应结构**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| success | boolean | 请求是否成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |

**地址信息字段**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| addresses | array | 地址列表 |
| addresses[].id | integer | 地址ID |
| addresses[].customer_id | integer | 客户ID |
| addresses[].company_name | string\|null | 公司名称 |
| addresses[].first_name | string | 名 |
| addresses[].last_name | string | 姓 |
| addresses[].full_name | string | 完整姓名 |
| addresses[].vat_id | string\|null | 增值税号 |
| addresses[].address | string | 完整地址（换行分隔） |
| addresses[].address_lines | array | 地址行数组 |
| addresses[].country | string | 国家代码（ISO 2位） |
| addresses[].state | string | 州/省 |
| addresses[].city | string | 城市 |
| addresses[].postcode | string | 邮政编码 |
| addresses[].phone | string | 联系电话 |
| addresses[].email | string | 邮箱地址 |
| addresses[].default_address | boolean | 是否为默认地址 |
| addresses[].created_at | datetime | 创建时间 |
| addresses[].updated_at | datetime | 更新时间 |
| total | integer | 地址总数 |

#### 📤 响应示例

```json
{
    "success": true,
    "message": "Success",
    "data": {
        "addresses": [
            {
                "id": 1,
                "customer_id": 3,
                "company_name": "Tech Company Ltd",
                "first_name": "John",
                "last_name": "Doe",
                "full_name": "John Doe",
                "vat_id": "VAT123456789",
                "address": "123 Main Street\nApartment 4B",
                "address_lines": [
                    "123 Main Street",
                    "Apartment 4B"
                ],
                "country": "US",
                "state": "California",
                "city": "Los Angeles",
                "postcode": "90210",
                "phone": "+1234567890",
                "email": "<EMAIL>",
                "default_address": true,
                "created_at": "2024-01-15T10:30:00.000000Z",
                "updated_at": "2024-01-15T14:20:00.000000Z"
            }
        ],
        "total": 1
    }
}
```

---

### 2. 创建新地址

**接口地址**: `POST /api/mlk/addresses`

**接口描述**: 为当前用户创建新的收货地址

#### 📥 请求参数

| 参数名称 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|-------|
| first_name | string | 是 | 名，最大255字符 | "John" |
| last_name | string | 是 | 姓，最大255字符 | "Doe" |
| company_name | string | 否 | 公司名称，最大255字符 | "Tech Company" |
| vat_id | string | 否 | 增值税号，最大255字符 | "VAT123456789" |
| address | array | 是 | 地址行数组，至少1行 | ["123 Main St", "Apt 4B"] |
| country | string | 是 | 国家代码，2位字母 | "US" |
| state | string | 是 | 州/省，最大255字符 | "California" |
| city | string | 是 | 城市，最大255字符 | "Los Angeles" |
| postcode | string | 是 | 邮政编码，最大20字符 | "90210" |
| phone | string | 是 | 联系电话，最大20字符 | "+1234567890" |
| email | string | 是 | 邮箱地址 | "<EMAIL>" |
| default_address | boolean | 否 | 是否设为默认地址 | true |

#### 📤 响应示例

```json
{
    "success": true,
    "message": "地址创建成功",
    "data": {
        "address": {
            "id": 2,
            "customer_id": 3,
            "company_name": "Tech Company",
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "vat_id": "VAT123456789",
            "address": "123 Main St\nApt 4B",
            "address_lines": [
                "123 Main St",
                "Apt 4B"
            ],
            "country": "US",
            "state": "California",
            "city": "Los Angeles",
            "postcode": "90210",
            "phone": "+1234567890",
            "email": "<EMAIL>",
            "default_address": true,
            "created_at": "2024-01-15T16:30:00.000000Z",
            "updated_at": "2024-01-15T16:30:00.000000Z"
        }
    }
}
```

---

### 3. 获取地址详情

**接口地址**: `GET /api/mlk/addresses/detail`

**接口描述**: 获取指定地址的详细信息

#### 📥 请求参数

| 参数名称 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|-------|
| address_id | integer | 是 | 地址ID | 1 |

#### 📤 响应示例

```json
{
    "success": true,
    "message": "Success",
    "data": {
        "address": {
            "id": 1,
            "customer_id": 3,
            "company_name": "Tech Company Ltd",
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "vat_id": "VAT123456789",
            "address": "123 Main Street\nApartment 4B",
            "address_lines": [
                "123 Main Street",
                "Apartment 4B"
            ],
            "country": "US",
            "state": "California",
            "city": "Los Angeles",
            "postcode": "90210",
            "phone": "+1234567890",
            "email": "<EMAIL>",
            "default_address": true,
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T14:20:00.000000Z"
        }
    }
}
```

---

### 4. 更新地址

**接口地址**: `POST /api/mlk/addresses/update`

**接口描述**: 更新指定地址的信息

#### 📥 请求参数

| 参数名称 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|-------|
| address_id | integer | 是 | 地址ID | 1 |
| first_name | string | 是 | 名，最大255字符 | "John" |
| last_name | string | 是 | 姓，最大255字符 | "Doe" |
| company_name | string | 否 | 公司名称，最大255字符 | "New Company" |
| vat_id | string | 否 | 增值税号，最大255字符 | "VAT987654321" |
| address | array | 是 | 地址行数组，至少1行 | ["456 New St", "Suite 5A"] |
| country | string | 是 | 国家代码，2位字母 | "US" |
| state | string | 是 | 州/省，最大255字符 | "New York" |
| city | string | 是 | 城市，最大255字符 | "New York" |
| postcode | string | 是 | 邮政编码，最大20字符 | "10001" |
| phone | string | 是 | 联系电话，最大20字符 | "+1987654321" |
| email | string | 是 | 邮箱地址 | "<EMAIL>" |
| default_address | boolean | 否 | 是否设为默认地址 | false |

#### 📤 响应示例

```json
{
    "success": true,
    "message": "地址更新成功",
    "data": {
        "address": {
            "id": 1,
            "customer_id": 3,
            "company_name": "New Company",
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "vat_id": "VAT987654321",
            "address": "456 New St\nSuite 5A",
            "address_lines": [
                "456 New St",
                "Suite 5A"
            ],
            "country": "US",
            "state": "New York",
            "city": "New York",
            "postcode": "10001",
            "phone": "+1987654321",
            "email": "<EMAIL>",
            "default_address": false,
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T17:45:00.000000Z"
        }
    }
}
```

---

### 5. 设置默认地址

**接口地址**: `POST /api/mlk/addresses/set-default`

**接口描述**: 将指定地址设置为默认收货地址

#### 📥 请求参数

| 参数名称 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|-------|
| address_id | integer | 是 | 地址ID | 1 |

#### 📤 响应示例

```json
{
    "success": true,
    "message": "默认地址设置成功",
    "data": {
        "address": {
            "id": 1,
            "customer_id": 3,
            "company_name": "Tech Company Ltd",
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe",
            "vat_id": "VAT123456789",
            "address": "123 Main Street\nApartment 4B",
            "address_lines": [
                "123 Main Street",
                "Apartment 4B"
            ],
            "country": "US",
            "state": "California",
            "city": "Los Angeles",
            "postcode": "90210",
            "phone": "+1234567890",
            "email": "<EMAIL>",
            "default_address": true,
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T18:00:00.000000Z"
        }
    }
}
```

---

### 6. 删除地址

**接口地址**: `DELETE /api/mlk/addresses/delete`

**接口描述**: 删除指定的地址

#### 📥 请求参数

| 参数名称 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|-------|
| address_id | integer | 是 | 地址ID | 1 |

#### 📤 响应示例

```json
{
    "success": true,
    "message": "地址删除成功",
    "data": {}
}
```

---

## 🔄 错误响应

### 认证失败
```json
{
    "success": false,
    "message": "Unauthenticated.",
    "code": 401
}
```

### 参数验证失败
```json
{
    "success": false,
    "message": "The first_name field is required.",
    "code": 422
}
```

### 地址不存在或无权访问
```json
{
    "success": false,
    "message": "地址不存在或无权访问",
    "code": 404
}
```

### 服务器错误
```json
{
    "success": false,
    "message": "Internal server error",
    "code": 500
}
```

---

## 📝 使用示例

### JavaScript/Axios 示例

```javascript
// 获取地址列表
const getAddresses = async () => {
    try {
        const response = await axios.get('/api/mlk/addresses', {
            headers: {
                'Authorization': 'Bearer ' + accessToken,
                'Accept': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('获取地址列表失败:', error.response?.data?.message);
        throw error;
    }
};

// 创建新地址
const createAddress = async (addressData) => {
    try {
        const response = await axios.post('/api/mlk/addresses', {
            first_name: 'John',
            last_name: 'Doe',
            company_name: 'Tech Company',
            address: ['123 Main Street', 'Apartment 4B'],
            country: 'US',
            state: 'California',
            city: 'Los Angeles',
            postcode: '90210',
            phone: '+1234567890',
            email: '<EMAIL>',
            default_address: true,
            ...addressData
        }, {
            headers: {
                'Authorization': 'Bearer ' + accessToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('创建地址失败:', error.response?.data?.message);
        throw error;
    }
};

// 更新地址
const updateAddress = async (addressId, addressData) => {
    try {
        const response = await axios.put('/api/mlk/addresses/update', {
            address_id: addressId,
            ...addressData
        }, {
            headers: {
                'Authorization': 'Bearer ' + accessToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('更新地址失败:', error.response?.data?.message);
        throw error;
    }
};

// 设置默认地址
const setDefaultAddress = async (addressId) => {
    try {
        const response = await axios.post('/api/mlk/addresses/set-default', {
            address_id: addressId
        }, {
            headers: {
                'Authorization': 'Bearer ' + accessToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('设置默认地址失败:', error.response?.data?.message);
        throw error;
    }
};

// 删除地址
const deleteAddress = async (addressId) => {
    try {
        const response = await axios.delete('/api/mlk/addresses/delete', {
            data: { address_id: addressId },
            headers: {
                'Authorization': 'Bearer ' + accessToken,
                'Accept': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('删除地址失败:', error.response?.data?.message);
        throw error;
    }
};
```

### cURL 示例

```bash
# 获取地址列表
curl -X GET "http://your-domain.com/api/mlk/addresses" \
  -H "Authorization: Bearer your_access_token" \
  -H "Accept: application/json"

# 创建新地址
curl -X POST "http://your-domain.com/api/mlk/addresses" \
  -H "Authorization: Bearer your_access_token" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "address": ["123 Main Street", "Apartment 4B"],
    "country": "US",
    "state": "California",
    "city": "Los Angeles",
    "postcode": "90210",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "default_address": true
  }'

# 更新地址
curl -X PUT "http://your-domain.com/api/mlk/addresses/update" \
  -H "Authorization: Bearer your_access_token" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "address_id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "address": ["456 New Street"],
    "country": "US",
    "state": "New York",
    "city": "New York",
    "postcode": "10001",
    "phone": "+1987654321",
    "email": "<EMAIL>"
  }'

# 删除地址
curl -X DELETE "http://your-domain.com/api/mlk/addresses/delete" \
  -H "Authorization: Bearer your_access_token" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{"address_id": 1}'
```

---

## 📖 字段类型说明

### 数据类型定义

| 类型 | 说明 | 示例 |
|------|------|------|
| integer | 整数 | `1`, `123`, `0` |
| string | 字符串 | `"John Doe"`, `"123 Main St"` |
| boolean | 布尔值 | `true`, `false` |
| datetime | 日期时间（ISO格式） | `"2024-01-15T10:30:00.000000Z"` |
| array | 数组 | `["line1", "line2"]` |
| object | 对象 | `{...}` |
| null | 空值 | `null` |

### 特殊字段说明

- **地址格式**: address字段存储为换行分隔的字符串，address_lines为便于前端使用的数组格式
- **国家代码**: 使用ISO 3166-1 alpha-2标准，如"US"、"CN"等
- **默认地址**: 每个用户只能有一个默认地址，设置新的默认地址会自动取消原有默认地址
- **必填字段**: 标记为必填的字段在创建和更新时都必须提供

---

## 🎯 功能特性

### ✅ 地址管理功能
- **完整CRUD**: 支持地址的创建、读取、更新、删除操作
- **默认地址**: 支持设置和管理默认收货地址
- **多行地址**: 支持多行地址输入，便于详细地址描述
- **严格验证**: 完整的输入参数验证和数据类型检查

### ✅ 安全特性
- **用户认证**: 所有接口需要Bearer Token认证
- **权限控制**: 用户只能操作自己的地址
- **参数验证**: 严格的输入参数验证
- **错误处理**: 统一的错误响应格式

### ✅ 数据完整性
- **事件系统**: 支持地址创建、更新、删除的事件触发
- **数据关联**: 自动处理客户ID关联
- **默认地址管理**: 自动处理默认地址的唯一性

---

## 🚀 最佳实践

1. **地址验证**: 前端应当验证地址格式的有效性
2. **默认地址处理**: 创建第一个地址时建议设为默认地址
3. **错误处理**: 妥善处理各种错误响应，提供用户友好的提示
4. **地址格式**: 建议使用多行地址输入提升用户体验
5. **国家州市联动**: 前端可实现国家、州、城市的联动选择
6. **电话号码验证**: 建议在前端验证电话号码格式
7. **邮政编码验证**: 根据不同国家验证邮政编码格式 