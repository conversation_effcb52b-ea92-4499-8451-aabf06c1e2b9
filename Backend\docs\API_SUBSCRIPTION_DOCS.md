# 订阅功能 API 文档

## 概述

本文档描述了邮件订阅功能的 API 接口，支持多种订阅类型的管理。

## 基础信息

- **基础URL**: `/api`
- **认证方式**: Bearer <PERSON>（可选，用于关联用户）
- **请求格式**: JSON
- **响应格式**: JSON

## 订阅类型说明

| 类型 | 值 | 描述 |
|------|-----|------|
| 新闻通讯 | `newsletter` | 定期发送的新闻和资讯（默认） |
| 促销信息 | `promotional` | 产品促销和特价信息 |
| 产品更新 | `updates` | 新产品发布和功能更新 |
| 所有类型 | `all` | 订阅所有类型的消息 |

---

## 1. 订阅接口

### 基本信息
- **接口路径**: `POST /api/subscribe`
- **接口描述**: 订阅邮件通知
- **是否需要认证**: 否（可选）

### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| email | string | 是 | 邮箱地址 | `<EMAIL>` |
| subscription_type | string | 否 | 订阅类型 | `newsletter` |

#### 订阅类型枚举值
- `newsletter` - 新闻通讯（默认值）
- `promotional` - 促销信息
- `updates` - 产品更新
- `all` - 所有类型

### 请求示例

```json
{
    "email": "<EMAIL>",
    "subscription_type": "newsletter"
}
```

### 响应格式

#### 成功响应 (200)
```json
{
    "success": true,
    "message": "您已成功订阅我们的新闻通讯。",
    "data": {
        "subscription": {
            "id": 1,
            "email": "<EMAIL>",
            "subscription_type": "newsletter",
            "is_subscribed": true,
            "customer_id": null,
            "channel_id": 1,
            "token": "abc123def456",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        }
    }
}
```

#### 错误响应 (400)
```json
{
    "success": false,
    "message": "您已经订阅了我们的新闻通讯。",
    "data": null,
    "errors": null
}
```

#### 验证错误 (422)
```json
{
    "success": false,
    "message": "验证失败",
    "data": null,
    "errors": {
        "email": [
            "邮箱地址格式不正确"
        ],
        "subscription_type": [
            "订阅类型必须是: newsletter, promotional, updates, all 中的一个"
        ]
    }
}
```

---

## 2. 取消订阅接口

### 基本信息
- **接口路径**: `POST /api/unsubscribe`
- **接口描述**: 取消邮件订阅
- **是否需要认证**: 否

### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| email | string | 条件必填* | 邮箱地址 | `<EMAIL>` |
| token | string | 条件必填* | 订阅令牌 | `abc123def456` |
| subscription_type | string | 否 | 订阅类型 | `newsletter` |

> *注意：email 和 token 至少提供一个

### 请求示例

#### 通过邮箱取消订阅
```json
{
    "email": "<EMAIL>",
    "subscription_type": "promotional"
}
```

#### 通过令牌取消订阅
```json
{
    "token": "abc123def456"
}
```

### 响应格式

#### 成功响应 (200)
```json
{
    "success": true,
    "message": "您已成功取消订阅我们的新闻通讯。",
    "data": []
}
```

#### 错误响应 (404)
```json
{
    "success": false,
    "message": "未找到该订阅信息",
    "data": null,
    "errors": null
}
```

---

## 3. 查询订阅状态接口

### 基本信息
- **接口路径**: `POST /api/subscription/status`
- **接口描述**: 查询邮箱订阅状态
- **是否需要认证**: 否

### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| email | string | 是 | 邮箱地址 | `<EMAIL>` |
| subscription_type | string | 否 | 订阅类型 | `newsletter` |

### 请求示例

#### 查询特定类型订阅状态
```json
{
    "email": "<EMAIL>",
    "subscription_type": "newsletter"
}
```

#### 查询所有订阅状态
```json
{
    "email": "<EMAIL>"
}
```

### 响应格式

#### 查询特定类型 - 已订阅 (200)
```json
{
    "success": true,
    "message": "成功",
    "data": {
        "email": "<EMAIL>",
        "subscription_type": "newsletter",
        "is_subscribed": true,
        "created_at": "2024-01-01T00:00:00.000000Z"
    }
}
```

#### 查询特定类型 - 未订阅 (200)
```json
{
    "success": true,
    "message": "成功",
    "data": {
        "email": "<EMAIL>",
        "subscription_type": "newsletter",
        "is_subscribed": false
    }
}
```

#### 查询所有订阅状态 (200)
```json
{
    "success": true,
    "message": "成功",
    "data": {
        "email": "<EMAIL>",
        "subscriptions": [
            {
                "subscription_type": "newsletter",
                "is_subscribed": true,
                "created_at": "2024-01-01T00:00:00.000000Z"
            },
            {
                "subscription_type": "promotional",
                "is_subscribed": false,
                "created_at": "2024-01-01T00:00:00.000000Z"
            }
        ]
    }
}
```

#### 无任何订阅 (200)
```json
{
    "success": true,
    "message": "成功",
    "data": {
        "email": "<EMAIL>",
        "subscriptions": []
    }
}
```

---

## 错误代码说明

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 200 | 成功 | 请求成功处理 |
| 400 | 业务错误 | 已订阅相同类型等业务逻辑错误 |
| 404 | 未找到 | 订阅信息不存在 |
| 422 | 验证错误 | 请求参数验证失败 |
| 500 | 服务器错误 | 服务器内部错误 |

---

## 使用场景示例

### 场景1：用户首次订阅新闻通讯
```bash
curl -X POST "http://your-domain.com/api/subscribe" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "subscription_type": "newsletter"
  }'
```

### 场景2：用户订阅多种类型
```bash
# 订阅新闻通讯
curl -X POST "http://your-domain.com/api/subscribe" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "subscription_type": "newsletter"}'

# 订阅促销信息
curl -X POST "http://your-domain.com/api/subscribe" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "subscription_type": "promotional"}'
```

### 场景3：取消特定类型订阅
```bash
curl -X POST "http://your-domain.com/api/unsubscribe" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "subscription_type": "promotional"
  }'
```

### 场景4：查询用户所有订阅状态
```bash
curl -X POST "http://your-domain.com/api/subscription/status" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

---

## 注意事项

1. **邮箱唯一性**: 同一邮箱可以订阅多种类型，但相同邮箱和订阅类型的组合是唯一的。

2. **用户关联**: 如果请求时提供了有效的认证令牌，系统会自动关联到对应的用户账户。

3. **令牌安全**: 每个订阅都有唯一的令牌，可用于无需邮箱的取消订阅操作。

4. **数据清理**: 取消订阅不会删除记录，只是将 `is_subscribed` 设为 `false`。

5. **用户状态同步**: 当用户的所有订阅都取消时，会自动更新用户资料中的订阅状态。

---

## 开发者信息

- **API 版本**: v1
- **最后更新**: 2024-01-01
- **维护者**: MLK Web API Team
- **技术栈**: Laravel + Bagisto 