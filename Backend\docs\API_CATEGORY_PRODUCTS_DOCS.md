# 分类产品API文档

## 概述

此API提供了通过多个分类ID查询分类及其子分类下所有产品列表的功能，支持分页和排序。

## 接口列表

### 1. 查询分类产品列表

**接口地址：** `POST /api/mlk/category/by-categories`

**功能描述：** 通过传入多个分类ID，查询这些分类及其子分类下的所有产品列表，支持分页和排序。

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| category_ids | array | 是 | 分类ID数组 |
| page | integer | 否 | 页码，默认为1 |
| limit | integer | 否 | 每页数量，默认为12，最大100 |
| sort | string | 否 | 排序字段，可选值：id, name, price, created_at, updated_at，默认为id |
| order | string | 否 | 排序方式，可选值：asc, desc，默认为desc |

#### 请求示例

```json
{
    "category_ids": [1, 2, 3],
    "page": 1,
    "limit": 12,
    "sort": "price",
    "order": "asc"
}
```

#### 响应示例

```json
{
    "success": true,
    "message": "分类产品查询成功",
    "data": {
        "products": {
            "data": [
                {
                    "id": 1,
                    "name": "产品名称",
                    "url_key": "product-url-key",
                    "price": 85.00,
                    "formatted_price": "$85.00",
                    "base_price": 100.00,
                    "formatted_base_price": "$100.00",
                    "short_description": "产品简短描述",
                    "description": "产品详细描述",
                    "base_image": "http://example.com/image.jpg",
                    "images": [
                        {
                            "id": 1,
                            "path": "product/1/image.jpg",
                            "url": "http://example.com/image.jpg"
                        }
                    ],
                    "is_new": false,
                    "is_featured": true,
                    "color": {
                        "option_id": 1,
                        "option_value": "red",
                        "option_label": "红色"
                    },
                    "discount": {
                        "has_discount": true,
                        "regular_price": 100.00,
                        "formatted_regular_price": "$100.00",
                        "final_price": 85.00,
                        "formatted_final_price": "$85.00",
                        "discount_amount": 15.00,
                        "formatted_discount_amount": "$15.00",
                        "discount_percentage": 15.00,
                        "special_price": 85.00,
                        "formatted_special_price": "$85.00",
                        "special_price_from": "2024-01-01",
                        "special_price_to": "2024-12-31",
                        "is_special_price_active": true,
                        "discount_sources": [
                            {
                                "type": "special_price",
                                "name": "Special Price",
                                "price": 85.00,
                                "from": "2024-01-01",
                                "to": "2024-12-31",
                                "savings": 15.00
                            }
                        ]
                    }
                }
            ],
            "pagination": {
                "current_page": 1,
                "last_page": 5,
                "per_page": 12,
                "total": 60,
                "from": 1,
                "to": 12,
                "has_more_pages": true
            }
        },
        "categories": [
            {
                "id": 1,
                "name": "分类名称",
                "slug": "category-slug",
                "parent_id": null
            }
        ],
        "total_category_ids": [1, 2, 3, 4, 5],
        "query_params": {
            "sort": "price",
            "order": "asc",
            "limit": 12
        }
    }
}
```

### 2. 查询分类树及产品数量统计

**接口地址：** `POST /api/mlk/category/category-tree-count`

**功能描述：** 获取指定分类及其子分类的产品数量统计。

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| category_ids | array | 是 | 分类ID数组 |

#### 请求示例

```json
{
    "category_ids": [1, 2]
}
```

#### 响应示例

```json
{
    "success": true,
    "message": "分类树查询成功",
    "data": {
        "categories": [
            {
                "category": {
                    "id": 1,
                    "name": "分类名称",
                    "slug": "category-slug",
                    "parent_id": null
                },
                "products_count": 25,
                "subcategory_ids": [4, 5, 6]
            }
        ]
    }
}
```

## 功能特点

1. **智能子分类查询**：自动包含指定分类的所有子分类
2. **灵活分页**：支持自定义每页数量和页码
3. **多维排序**：支持按多个字段排序
4. **完整分页信息**：提供详细的分页元数据
5. **分类信息返回**：同时返回查询的分类信息
6. **产品数量统计**：提供分类及子分类的产品数量统计
7. **丰富的产品信息**：包含产品的颜色属性、折扣信息、图片等详细数据
8. **统一格式化**：与IndexController保持一致的产品数据格式
9. **性能优化**：预加载相关数据，避免N+1查询问题
10. **折扣详情**：提供完整的折扣来源和计算信息

## 错误处理

API会返回标准的错误响应格式：

```json
{
    "success": false,
    "message": "错误信息",
    "errors": {
        "category_ids": ["分类ID必须是数组"]
    }
}
```

## 使用建议

1. 建议设置合理的limit值（12-24），避免一次性查询过多数据
2. 可以先使用分类树统计接口了解各分类的产品数量
3. 根据业务需要选择合适的排序字段和方式
4. 注意检查返回的total_category_ids，了解实际查询的分类范围

## 注意事项

- 只会查询状态为启用(status=1)的分类和产品
- 只会查询可单独显示(visible_individually=1)的产品
- 分类ID必须存在于数据库中，否则会返回验证错误
- 子分类查询基于Bagisto的分类树结构 