# 用户优惠券 API 接口文档

## 概述

用户优惠券 API 提供了管理和查询用户优惠券的功能。用户可以通过这些接口查看自己的优惠券列表、状态和详细信息。

**基础 URL**: `/api/mlk`

**认证方式**: Bearer Token (Sanctum)

**Content-Type**: `application/json`

---

## 接口列表

### 1. 获取用户优惠券列表

获取当前用户的优惠券列表，支持分页、筛选和统计功能。

#### 基本信息

- **URL**: `/api/mlk/coupons`
- **方法**: `GET`
- **认证**: 需要 (Bearer Token)

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `status` | string | 否 | - | 按状态筛选: `active`, `used`, `expired`, `revoked` |
| `available_only` | boolean | 否 | false | 是否只获取可用优惠券 |
| `page` | integer | 否 | 1 | 页码，最小值为 1 |
| `per_page` | integer | 否 | 15 | 每页数量，范围 1-50 |

#### 请求示例

```bash
# 获取所有优惠券
GET /api/mlk/coupons
Authorization: Bearer your_access_token

# 只获取可用优惠券
GET /api/mlk/coupons?available_only=true

# 按状态筛选
GET /api/mlk/coupons?status=active

# 分页查询
GET /api/mlk/coupons?page=2&per_page=20

# 组合查询
GET /api/mlk/coupons?status=active&page=1&per_page=10
```

#### 响应格式

**成功响应 (200)**:

```json
{
  "success": true,
  "message": "获取优惠券列表成功",
  "data": {
    "coupons": [
      {
        "id": 1,
        "coupon_code": "WELCOME2024",
        "status": "active",
        "status_label": "可用",
        "usage_limit": 5,
        "times_used": 1,
        "remaining_uses": 4,
        "issued_at": "2024-12-19 10:30:00",
        "issued_at_human": "2天前",
        "used_at": "2024-12-20 15:45:00",
        "used_at_human": "1天前",
        "expired_at": "2024-12-31 23:59:59",
        "expired_at_human": "12天后",
        "issued_reason": "新用户欢迎礼包",
        "is_available": true,
        "can_use": true,
        "created_at": "2024-12-19 10:30:00",
        "updated_at": "2024-12-20 15:45:00",
        "cart_rule_coupon": {
          "id": 5,
          "code": "WELCOME2024",
          "usage_limit": 1000,
          "usage_per_customer": 5,
          "times_used": 150,
          "type": 1,
          "expired_at": "2024-12-31 23:59:59",
          "is_primary": true
        },
        "cart_rule": {
          "id": 3,
          "name": "新用户欢迎优惠",
          "description": "新用户注册专享优惠券",
          "discount_amount": 50.00,
          "action_type": "by_fixed",
          "action_type_label": "固定金额折扣",
          "starts_from": "2024-12-01 00:00:00",
          "ends_till": "2024-12-31 23:59:59",
          "status": 1,
          "is_active": true
        }
      }
    ],
    "pagination": {
      "total": 25,
      "per_page": 15,
      "current_page": 1,
      "last_page": 2,
      "from": 1,
      "to": 15
    },
    "stats": {
      "total_coupons": 25,
      "total_uses": 12,
      "active": 15,
      "used": 8,
      "expired": 2,
      "revoked": 0
    }
  },
  "locale": "zh_CN"
}
```

#### 字段说明

##### 优惠券基础信息

| 字段 | 类型 | 说明 |
|------|------|------|
| `id` | integer | 用户优惠券 ID |
| `coupon_code` | string | 优惠券代码 |
| `status` | string | 状态: `active`(可用), `used`(已使用), `expired`(已过期), `revoked`(已撤销) |
| `status_label` | string | 状态中文标签 |
| `usage_limit` | integer | 使用次数限制 |
| `times_used` | integer | 已使用次数 |
| `remaining_uses` | integer | 剩余使用次数 |
| `issued_at` | string\|null | 发放时间 |
| `issued_at_human` | string\|null | 发放时间人性化显示 |
| `used_at` | string\|null | 首次使用时间 |
| `used_at_human` | string\|null | 使用时间人性化显示 |
| `expired_at` | string\|null | 过期时间 |
| `expired_at_human` | string\|null | 过期时间人性化显示 |
| `issued_reason` | string\|null | 发放原因/备注 |
| `is_available` | boolean | 是否可用 (模型方法) |
| `can_use` | boolean | 是否可以使用 (综合判断) |

##### 购物车规则优惠券信息 (cart_rule_coupon)

| 字段 | 类型 | 说明 |
|------|------|------|
| `id` | integer | 购物车规则优惠券 ID |
| `code` | string | 优惠券原始代码 |
| `usage_limit` | integer | 总使用次数限制 |
| `usage_per_customer` | integer | 每个用户使用次数限制 |
| `times_used` | integer | 总已使用次数 |
| `type` | integer | 优惠券类型 |
| `expired_at` | string\|null | 原始过期时间 |
| `is_primary` | boolean | 是否为主要优惠券 |

##### 购物车规则信息 (cart_rule)

| 字段 | 类型 | 说明 |
|------|------|------|
| `id` | integer | 购物车规则 ID |
| `name` | string | 规则名称 |
| `description` | string\|null | 规则描述 |
| `discount_amount` | float | 折扣金额 |
| `action_type` | string | 折扣类型: `by_percent`, `by_fixed`, `cart_fixed`, `buy_x_get_y` |
| `action_type_label` | string | 折扣类型中文标签 |
| `starts_from` | string\|null | 规则开始时间 |
| `ends_till` | string\|null | 规则结束时间 |
| `status` | integer | 规则状态: 1(启用), 0(禁用) |
| `is_active` | boolean | 是否当前有效 |

##### 分页信息 (pagination)

| 字段 | 类型 | 说明 |
|------|------|------|
| `total` | integer | 总记录数 |
| `per_page` | integer | 每页记录数 |
| `current_page` | integer | 当前页码 |
| `last_page` | integer | 最后页码 |
| `from` | integer | 当前页开始记录位置 |
| `to` | integer | 当前页结束记录位置 |

##### 统计信息 (stats)

| 字段 | 类型 | 说明 |
|------|------|------|
| `total_coupons` | integer | 用户优惠券总数 |
| `total_uses` | integer | 总使用次数 |
| `active` | integer | 可用优惠券数量 |
| `used` | integer | 已使用优惠券数量 |
| `expired` | integer | 已过期优惠券数量 |
| `revoked` | integer | 已撤销优惠券数量 |

#### 错误响应

**未授权 (401)**:
```json
{
  "success": false,
  "message": "请先登录",
  "data": [],
  "locale": "zh_CN"
}
```

**参数验证错误 (422)**:
```json
{
  "success": false,
  "message": "参数验证失败",
  "data": {
    "errors": ["status字段必须是以下值之一: active, used, expired, revoked"]
  },
  "locale": "zh_CN"
}
```

**服务器错误 (400)**:
```json
{
  "success": false,
  "message": "获取优惠券列表失败: 具体错误信息",
  "data": [],
  "locale": "zh_CN"
}
```

---

## 业务逻辑说明

### 优惠券状态判断

1. **`active` (可用)**: 优惠券处于活跃状态，可以使用
2. **`used` (已使用)**: 优惠券使用次数已达到限制
3. **`expired` (已过期)**: 优惠券已过过期时间
4. **`revoked` (已撤销)**: 优惠券被管理员撤销

### 可用性判断逻辑

优惠券的 `can_use` 字段会综合考虑以下因素：

1. **基本状态**: 必须为 `active`
2. **过期时间**: 未过期或无过期时间限制
3. **使用次数**: 未达到使用次数限制
4. **购物车规则状态**: 关联的购物车规则必须启用
5. **购物车规则有效期**: 购物车规则在有效期内

### 筛选功能

- **状态筛选**: 可按 `status` 参数筛选特定状态的优惠券
- **可用性筛选**: `available_only=true` 时只返回可用的优惠券
- **分页**: 支持分页查询，避免数据量过大

---

## 使用场景示例

### 1. 用户优惠券中心页面

```javascript
// 获取用户所有优惠券及统计信息
fetch('/api/mlk/coupons', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  // 显示优惠券列表
  displayCoupons(data.data.coupons);
  // 显示统计信息
  displayStats(data.data.stats);
  // 显示分页组件
  displayPagination(data.data.pagination);
});
```

### 2. 购物车页面可用优惠券

```javascript
// 获取可用优惠券供用户选择
fetch('/api/mlk/coupons?available_only=true', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  // 显示可用优惠券列表供用户选择
  displayAvailableCoupons(data.data.coupons);
});
```

### 3. 优惠券历史记录

```javascript
// 查看已使用的优惠券
fetch('/api/mlk/coupons?status=used&per_page=20', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  // 显示使用历史
  displayCouponHistory(data.data.coupons);
});
```

---

## 注意事项

1. **认证要求**: 所有接口都需要用户登录认证
2. **权限控制**: 用户只能查看自己的优惠券
3. **数据安全**: 敏感信息已过滤，不会泄露其他用户数据
4. **性能优化**: 使用了 Eloquent 关系预加载，减少数据库查询
5. **错误处理**: 提供详细的错误信息和状态码
6. **本地化**: 支持中文本地化显示

---

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2024-12-21 | 初始版本，基础优惠券查询功能 | 