<?php

namespace Webkul\Admin\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AdminLocaleController extends Controller
{
    /**
     * 支持的后台语言列表
     */
    protected $supportedLocales = [
        'en' => 'English',
        'zh_CN' => '中文',
        'it' => 'Italiano',
    ];

    /**
     * 切换后台语言
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function switch(Request $request)
    {
        $locale = $request->input('locale');
        
        // 验证语言代码是否在支持列表中
        if (!array_key_exists($locale, $this->supportedLocales)) {
            if ($request->expectsJson()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Unsupported locale: ' . $locale
                ], 400);
            }
            
            return redirect()->back()->with('error', 'Unsupported locale: ' . $locale);
        }
        
        // 将语言设置存储到session和cookie中
        session(['admin_locale' => $locale]);
        $this->setAdminLocaleCookie($locale);
        
        if ($request->expectsJson()) {
            return new JsonResponse([
                'success' => true,
                'message' => 'Language switched successfully',
                'locale' => $locale,
                'locale_name' => $this->supportedLocales[$locale]
            ]);
        }
        
        return redirect()->back()->with('success', 'Language switched to ' . $this->supportedLocales[$locale]);
    }
    
    /**
     * 获取当前后台语言
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function current()
    {
        // 优先从Cookie获取，然后是Session，最后是默认值
        $currentLocale = request()->cookie('admin_locale') 
            ?? session('admin_locale', 'en');
            
        // 验证语言是否支持
        if (!array_key_exists($currentLocale, $this->supportedLocales)) {
            $currentLocale = 'en';
        }
        
        return new JsonResponse([
            'current_locale' => $currentLocale,
            'current_locale_name' => $this->supportedLocales[$currentLocale] ?? 'English',
            'supported_locales' => $this->supportedLocales
        ]);
    }
    
    /**
     * 设置管理员语言Cookie
     *
     * @param  string  $locale
     * @return void
     */
    protected function setAdminLocaleCookie($locale)
    {
        // 动态获取后台路径前缀，确保路径格式正确
        $adminUrl = config('app.admin_url', 'admin');
        $adminPath = '/' . trim($adminUrl, '/');
        
        // 设置Cookie，有效期为1年
        cookie()->queue(cookie(
            'admin_locale',     // Cookie名称
            $locale,           // Cookie值
            60 * 24 * 365,     // 有效期：1年 (分钟)
            $adminPath,        // 路径：从配置中动态获取后台路径
            null,              // 域名（使用默认）
            false,             // secure：不要求HTTPS
            true,              // httpOnly：防止XSS攻击
            false,             // raw：不使用原始值
            'Lax'              // sameSite策略
        ));
    }
}
