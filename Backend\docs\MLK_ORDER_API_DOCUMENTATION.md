# MLK 订单管理 API 文档

## 概述
本文档描述了 MLKWebAPI 模块新增的订单管理相关接口，补全了与 Shop 模块对等的订单功能。

## 基础信息
- **基础路径**: `/api/mlk/orders`
- **认证方式**: <PERSON><PERSON> (Sanctum)
- **响应格式**: JSON

## 接口列表

### 1. 获取订单列表
```http
GET /api/mlk/orders
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| per_page | integer | 否 | 每页数量，默认10，最大50 |
| status | string | 否 | 订单状态筛选 |
| order_by | string | 否 | 排序字段 |
| sort | string | 否 | 排序方向 (asc/desc) |

**状态值**: `pending`, `processing`, `completed`, `canceled`, `closed`, `fraud`

**响应示例**:
```json
{
    "success": true,
    "data": {
        "orders": [
            {
                "id": 1,
                "increment_id": "000000001",
                "status": "pending",
                "grand_total": 299.99,
                "created_at": "2024-01-15T10:30:00Z",
                "items": [...]
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 5,
            "total": 50
        }
    }
}
```

### 2. 获取订单详情
```http
GET /api/mlk/orders/{id}
```

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | integer | 是 | 订单ID |

**响应示例**:
```json
{
    "success": true,
    "data": {
        "order": {
            "id": 1,
            "increment_id": "000000001",
            "status": "pending",
            "grand_total": 299.99,
            "items": [...],
            "billing_address": {...},
            "payment_method": {...},
            "shipments": [...],
            "invoices": [...]
        }
    }
}
```

### 3. 重新下单
```http
POST /api/mlk/orders/{id}/reorder
```

**功能**: 将历史订单中的商品重新添加到购物车

**响应示例**:
```json
{
    "success": true,
    "data": {
        "message": "Items added to cart successfully",
        "added_items": [
            {
                "name": "iPhone 15 Pro",
                "qty": 1
            }
        ],
        "failed_items": [],
        "cart_items_count": 3
    }
}
```

### 4. 取消订单
```http
POST /api/mlk/orders/{id}/cancel
```

**功能**: 取消指定订单（仅限 pending/processing 状态）

**响应示例**:
```json
{
    "success": true,
    "data": {
        "message": "Order canceled successfully",
        "order_id": 1,
        "increment_id": "000000001",
        "status": "canceled"
    }
}
```

### 5. 下载发票
```http
GET /api/mlk/orders/{invoice_id}/invoice
```

**功能**: 下载订单发票PDF文件

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| invoice_id | integer | 是 | 发票ID |

**响应**: PDF文件流

### 6. 订单状态统计
```http
GET /api/mlk/orders/stats/status
```

**功能**: 获取用户订单各状态的数量统计

**响应示例**:
```json
{
    "success": true,
    "data": {
        "stats": {
            "pending": 2,
            "processing": 1,
            "completed": 15,
            "canceled": 1,
            "closed": 0,
            "fraud": 0,
            "total": 19
        }
    }
}
```

## 错误响应
所有接口遵循统一的错误响应格式：

```json
{
    "success": false,
    "message": "错误描述",
    "code": 404
}
```

**常见错误码**:
- `401`: 未认证
- `404`: 订单/发票不存在
- `400`: 请求参数错误或订单状态不允许操作
- `500`: 服务器内部错误

## 认证说明
所有接口都需要在请求头中包含认证令牌：

```http
Authorization: Bearer {your_access_token}
```

## 使用示例

### 获取订单列表
```bash
curl -X GET "https://your-domain.com/api/mlk/orders?page=1&per_page=10&status=pending" \
  -H "Authorization: Bearer your_token" \
  -H "Accept: application/json"
```

### 重新下单
```bash
curl -X POST "https://your-domain.com/api/mlk/orders/1/reorder" \
  -H "Authorization: Bearer your_token" \
  -H "Accept: application/json"
```

### 取消订单
```bash
curl -X POST "https://your-domain.com/api/mlk/orders/1/cancel" \
  -H "Authorization: Bearer your_token" \
  -H "Accept: application/json"
```

## 注意事项
1. 所有时间字段均为 UTC 时间格式
2. 金额字段为浮点数，单位为系统基础货币
3. 订单取消仅限于 `pending` 和 `processing` 状态
4. 发票下载需要发票状态为 `paid`
5. 重新下单时，不可用商品会在 `failed_items` 中返回原因

## 更新日志
- **2024-01-15**: 初始版本，新增完整订单管理功能
- 补全了与 Shop 模块对等的订单功能
- 支持订单列表、详情、重新下单、取消、发票下载等功能
