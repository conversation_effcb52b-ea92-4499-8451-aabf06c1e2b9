# Shop包支付相关API详细文档

## 概述

本文档详细说明了Webkul Shop包中所有与支付相关的API接口，包括支付方式选择、支付处理、支付回调等功能。

## 基础信息

- **包名**: `Webkul\Shop`
- **基础URL**: `/api`
- **认证方式**: Session认证 (部分接口需要客户登录)
- **中间件**: `web`, `shop`

## 1. 结账流程中的支付API

### 1.1 获取支持的支付方式

**接口**: `GET /api/checkout/onepage/summary`

**描述**: 在结账摘要中获取可用的支付方式列表

**请求示例**:
```bash
GET /api/checkout/onepage/summary
```

**响应示例**:
```json
{
    "data": {
        "cart": {
            "id": 1,
            "items": [...],
            "payment_methods": [
                {
                    "method": "cashondelivery",
                    "method_title": "Cash On Delivery",
                    "description": "Cash On Delivery",
                    "sort": 1
                },
                {
                    "method": "moneytransfer",
                    "method_title": "Money Transfer", 
                    "description": "Money Transfer",
                    "sort": 2
                }
            ]
        }
    }
}
```

### 1.2 保存支付方式

**接口**: `POST /api/checkout/onepage/payment-methods`

**描述**: 为当前购物车保存选择的支付方式

**请求参数**:
```json
{
    "payment": {
        "method": "cashondelivery",
        "additional_data": {
            "notes": "请在工作时间配送"
        }
    }
}
```

**参数说明**:
- `payment.method` (必填): 支付方式代码
- `payment.additional_data` (可选): 支付方式的额外数据

**响应示例**:
```json
{
    "cart": {
        "id": 1,
        "payment_method": "cashondelivery",
        "payment_method_title": "Cash On Delivery",
        "grand_total": 299.99
    }
}
```

**错误响应**:
```json
{
    "redirect_url": "http://example.com/checkout/cart"
}
```
*HTTP状态码: 403*

### 1.3 提交订单 (包含支付处理)

**接口**: `POST /api/checkout/onepage/orders`

**描述**: 提交订单并处理支付，根据支付方式决定是否需要重定向

**响应类型1 - 需要重定向支付** (如PayPal):
```json
{
    "data": {
        "redirect": true,
        "redirect_url": "https://www.paypal.com/cgi-bin/webscr?..."
    }
}
```

**响应类型2 - 无需重定向支付** (如货到付款):
```json
{
    "data": {
        "redirect": true,
        "redirect_url": "http://example.com/checkout/onepage/success"
    }
}
```

**错误响应**:
```json
{
    "message": "订单验证失败的具体原因"
}
```
*HTTP状态码: 500*

## 2. PayPal支付专用API

### 2.1 PayPal Standard支付流程

#### 2.1.1 支付重定向

**接口**: `GET /paypal/standard/redirect`

**描述**: 重定向到PayPal支付页面

**流程**:
1. 用户选择PayPal支付方式
2. 提交订单后重定向到此接口
3. 页面自动跳转到PayPal支付网关

#### 2.1.2 支付成功回调

**接口**: `GET /paypal/standard/success`

**描述**: PayPal支付成功后的回调处理

**处理逻辑**:
1. 获取当前购物车信息
2. 创建订单记录
3. 停用购物车
4. 重定向到成功页面

**实现代码**:
```php
public function success()
{
    $cart = Cart::getCart();
    $data = (new OrderResource($cart))->jsonSerialize();
    $order = $this->orderRepository->create($data);
    
    Cart::deActivateCart();
    session()->flash('order_id', $order->id);
    
    return redirect()->route('shop.checkout.onepage.success');
}
```

#### 2.1.3 支付取消回调

**接口**: `GET /paypal/standard/cancel`

**描述**: PayPal支付取消后的处理

**处理逻辑**:
1. 设置错误消息
2. 重定向回购物车页面

#### 2.1.4 PayPal IPN处理

**接口**: `POST /paypal/standard/ipn`

**描述**: 处理PayPal的即时支付通知(IPN)

**功能**:
- 验证支付状态
- 更新订单状态
- 创建发票记录

### 2.2 PayPal Smart Button支付流程

#### 2.2.1 创建PayPal订单

**接口**: `GET /paypal/smart-button/create-order`

**描述**: 为PayPal Smart Button创建支付订单

**响应示例**:
```json
{
    "id": "PAYPAL_ORDER_ID",
    "status": "CREATED",
    "links": [...]
}
```

#### 2.2.2 捕获PayPal支付

**接口**: `POST /paypal/smart-button/capture-order`

**描述**: 捕获PayPal支付并创建系统订单

**请求参数**:
```json
{
    "orderData": {
        "orderID": "PAYPAL_ORDER_ID"
    }
}
```

**成功响应**:
```json
{
    "success": true
}
```

**处理流程**:
1. 调用PayPal API捕获支付
2. 验证支付金额
3. 创建系统订单
4. 更新订单状态为"processing"
5. 自动创建发票
6. 停用购物车

## 3. 支付方式配置

### 3.1 支持的支付方式

| 支付方式 | 代码 | 描述 | 重定向 |
|---------|------|------|--------|
| 货到付款 | `cashondelivery` | 收货时付款 | 否 |
| 银行转账 | `moneytransfer` | 线下银行转账 | 否 |
| PayPal标准 | `paypal_standard` | PayPal网站支付 | 是 |
| PayPal智能按钮 | `paypal_smart_button` | 页面内PayPal支付 | 否 |

### 3.2 支付方式获取逻辑

```php
// 在OnepageController中
public function storeAddress()
{
    // ... 地址保存逻辑
    
    if ($cart->haveStockableItems()) {
        // 有实物商品，需要配送
        if (!$rates = Shipping::collectRates()) {
            return redirect_to_cart();
        }
        return shipping_methods($rates);
    }
    
    // 无实物商品或已选择配送方式，返回支付方式
    return Payment::getSupportedPaymentMethods();
}
```

## 4. 支付验证和安全

### 4.1 订单验证

在创建订单前，系统会进行以下验证：

```php
public function validateOrder()
{
    $cart = Cart::getCart();
    $minimumOrderAmount = core()->getConfigData('sales.order_settings.minimum_order.minimum_order_amount') ?: 0;
    
    // 检查最小订单金额
    if (!Cart::haveMinimumOrderAmount()) {
        throw new \Exception('订单金额不满足最小要求');
    }
    
    // 检查库存
    if (!Cart::hasStockableItems()) {
        throw new \Exception('商品库存不足');
    }
    
    // 检查地址信息
    if ($cart->haveStockableItems() && !$cart->shipping_address) {
        throw new \Exception('配送地址不能为空');
    }
    
    // 检查支付方式
    if (!$cart->payment) {
        throw new \Exception('请选择支付方式');
    }
}
```

### 4.2 支付安全措施

1. **CSRF保护**: 所有POST请求都包含CSRF令牌
2. **会话验证**: 验证购物车归属权
3. **金额验证**: PayPal回调时验证支付金额
4. **状态检查**: 防止重复支付处理
5. **签名验证**: PayPal IPN签名验证

## 5. 错误处理

### 5.1 常见错误类型

| 错误类型 | HTTP状态码 | 处理方式 |
|---------|-----------|---------|
| 购物车错误 | 403 | 重定向到购物车页面 |
| 验证失败 | 500 | 返回错误消息 |
| 支付失败 | 400 | 返回PayPal错误信息 |
| 网络异常 | 500 | 记录日志并提示用户 |

### 5.2 错误处理示例

```php
// 支付方式保存错误处理
if (Cart::hasError() || !Cart::savePaymentMethod($payment)) {
    return response()->json([
        'redirect_url' => route('shop.checkout.cart.index'),
    ], Response::HTTP_FORBIDDEN);
}

// PayPal支付错误处理
try {
    return response()->json($this->smartButton->createOrder($requestBody));
} catch (\Exception $e) {
    return response()->json(json_decode($e->getMessage()), 400);
}
```

## 6. 集成示例

### 6.1 完整支付流程示例

```javascript
// 1. 保存支付方式
const paymentResponse = await fetch('/api/checkout/onepage/payment-methods', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': csrfToken
    },
    body: JSON.stringify({
        payment: {
            method: 'paypal_standard'
        }
    })
});

// 2. 提交订单
const orderResponse = await fetch('/api/checkout/onepage/orders', {
    method: 'POST',
    headers: {
        'X-CSRF-TOKEN': csrfToken
    }
});

const orderData = await orderResponse.json();

// 3. 处理支付重定向
if (orderData.data.redirect) {
    window.location.href = orderData.data.redirect_url;
}
```

### 6.2 PayPal Smart Button集成

```javascript
// PayPal Smart Button配置
paypal.Buttons({
    createOrder: function() {
        return fetch('/paypal/smart-button/create-order')
            .then(response => response.json())
            .then(data => data.id);
    },
    
    onApprove: function(data) {
        return fetch('/paypal/smart-button/capture-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                orderData: data
            })
        }).then(response => {
            if (response.ok) {
                window.location.href = '/checkout/onepage/success';
            }
        });
    }
}).render('#paypal-button-container');
```

## 总结

Shop包提供了完整的支付API体系，支持多种支付方式和支付流程。主要特点：

- **标准化接口**: 统一的API设计模式
- **多支付方式**: 支持货到付款、银行转账、PayPal等
- **安全可靠**: 完善的验证和错误处理机制
- **易于集成**: 清晰的接口文档和示例代码
- **扩展性强**: 支持自定义支付方式扩展

总计提供 **8个核心支付相关API接口**，满足电商平台的基本支付需求。
