# 商品搜索API文档

## 概述
商品搜索API提供强大的产品搜索功能，支持关键词搜索、分类筛选、属性过滤、价格范围筛选等多种搜索方式。

## 基础信息
- **接口地址**: `/api/mlk/search`
- **请求方式**: `GET`
- **认证要求**: 无需认证（公开接口）
- **中间件**: `api_locale`, `api_auth`

## 请求参数

### 基础搜索参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| query | string | 否 | 搜索关键词 | `手机`, `iPhone` |
| category_id | integer | 否 | 分类ID | `1` |
| price | string | 否 | 价格范围，格式：`最小价格,最大价格` | `100,500` |
| sort | string | 否 | 排序字段 | `price`, `name`, `created_at` |
| order | string | 否 | 排序方向 | `asc`, `desc` |
| page | integer | 否 | 页码，默认为1 | `1` |
| limit | integer | 否 | 每页数量，默认为12 | `20` |

### 属性筛选参数
支持通过产品属性进行筛选，参数名为属性代码：

| 参数名示例 | 类型 | 描述 | 示例 |
|-----------|------|------|------|
| color | string | 颜色筛选，多个值用逗号分隔 | `red,blue` |
| size | string | 尺寸筛选 | `L,XL` |
| brand | string | 品牌筛选 | `Apple,Samsung` |

## 请求示例

### 基础搜索
```bash
GET /api/mlk/search?query=手机&page=1&limit=10
```

### 分类搜索
```bash
GET /api/mlk/search?category_id=5&sort=price&order=asc
```

### 复合筛选
```bash
GET /api/mlk/search?query=手机&category_id=5&price=1000,3000&color=black,white&page=1&limit=20
```

### 价格排序
```bash
GET /api/mlk/search?sort=price&order=desc&limit=15
```

## 响应格式

### 成功响应
```json
{
    "success": true,
    "message": "搜索成功",
    "data": {
        "total": 156,
        "per_page": 12,
        "current_page": 1,
        "last_page": 13,
        "products": [
            {
                "id": 1,
                "type": "simple",
                "sku": "PROD-001",
                "name": "苹果iPhone 15",
                "url_key": "apple-iphone-15",
                "new": true,
                "featured": false,
                "visible_individually": true,
                "status": 1,
                "price": 5999.00,
                "formatted_price": "¥5,999.00",
                "special_price": 5499.00,
                "formatted_special_price": "¥5,499.00",
                "regular_price": 5999.00,
                "formatted_regular_price": "¥5,999.00",
                "min_price": 5499.00,
                "max_price": 5999.00,
                "base_image": "https://example.com/images/iphone15.jpg",
                "small_image": "https://example.com/images/iphone15_small.jpg",
                "thumbnail": "https://example.com/images/iphone15_thumb.jpg",
                "images": [
                    {
                        "id": 1,
                        "path": "product/1/iphone15.jpg",
                        "url": "https://example.com/storage/product/1/iphone15.jpg",
                        "small_image_url": "https://example.com/storage/product/1/small_iphone15.jpg",
                        "medium_image_url": "https://example.com/storage/product/1/medium_iphone15.jpg",
                        "large_image_url": "https://example.com/storage/product/1/large_iphone15.jpg",
                        "original_image_url": "https://example.com/storage/product/1/original_iphone15.jpg"
                    }
                ],
                "description": "最新一代iPhone，搭载A17芯片",
                "short_description": "苹果最新旗舰手机",
                "in_stock": true,
                "is_saved_for_later": false,
                "categories": [
                    {
                        "id": 5,
                        "name": "智能手机",
                        "slug": "smartphones",
                        "url": "https://example.com/category/smartphones"
                    }
                ],
                "attributes": [
                    {
                        "id": 23,
                        "code": "color",
                        "name": "颜色",
                        "type": "select",
                        "value": "black",
                        "label": "黑色",
                        "admin_name": "Color",
                        "is_filterable": true
                    },
                    {
                        "id": 24,
                        "code": "storage",
                        "name": "存储容量",
                        "type": "select",
                        "value": "128gb",
                        "label": "128GB",
                        "admin_name": "Storage",
                        "is_filterable": true
                    }
                ],
                "reviews_count": 25,
                "reviews_avg_rating": 4.5,
                "created_at": "2024-01-01T00:00:00.000000Z",
                "updated_at": "2024-01-15T10:30:00.000000Z"
            }
        ],
        "filterable_attributes": [
            {
                "id": 23,
                "code": "color",
                "name": "颜色",
                "type": "select",
                "options": [
                    {
                        "id": 1,
                        "admin_name": "Black",
                        "label": "黑色",
                        "swatch_value": "#000000"
                    },
                    {
                        "id": 2,
                        "admin_name": "White",
                        "label": "白色",
                        "swatch_value": "#FFFFFF"
                    }
                ]
            },
            {
                "id": 24,
                "code": "brand",
                "name": "品牌",
                "type": "select",
                "options": [
                    {
                        "id": 10,
                        "admin_name": "Apple",
                        "label": "苹果",
                        "swatch_value": null
                    }
                ]
            }
        ],
        "applied_filters": {
            "color": {
                "attribute": {
                    "id": 23,
                    "code": "color",
                    "name": "颜色",
                    "type": "select"
                },
                "value": "black,white"
            },
            "price": {
                "attribute": {
                    "code": "price",
                    "name": "价格",
                    "type": "price"
                },
                "value": {
                    "min": 1000,
                    "max": 5000
                }
            }
        }
    }
}
```

### 重定向响应
当搜索词有设置重定向时：
```json
{
    "success": true,
    "message": "搜索重定向",
    "data": {
        "redirect_url": "https://example.com/special-promotion"
    }
}
```

### 错误响应
```json
{
    "success": false,
    "message": "搜索关键词格式错误",
    "errors": {
        "query": [
            "搜索关键词不能包含反斜杠字符"
        ]
    }
}
```

## 可配置产品特殊字段

对于`type`为`configurable`的产品，会额外包含以下字段：

### variants（产品变体）
```json
"variants": [
    {
        "id": 101,
        "sku": "IPHONE15-128-BLACK",
        "name": "iPhone 15 128GB 黑色",
        "price": 5999.00,
        "formatted_price": "¥5,999.00",
        "in_stock": true
    }
]
```

### super_attributes（可配置属性）
```json
"super_attributes": [
    {
        "id": 23,
        "code": "color",
        "name": "颜色",
        "type": "select",
        "options": [
            {
                "id": 1,
                "label": "黑色",
                "value": 1
            },
            {
                "id": 2,
                "label": "白色",
                "value": 2
            }
        ]
    }
]
```

## 搜索功能特性

### 1. 智能搜索词记录
- 自动记录搜索关键词和结果数量
- 仅在纯关键词搜索时记录（不包含其他筛选条件）
- 支持搜索词统计和分析

### 2. 搜索引擎支持
- 支持数据库搜索（默认）
- 支持Elasticsearch搜索引擎
- 可通过配置切换搜索模式

### 3. 多条件筛选
- 关键词搜索：支持产品名称、描述等全文搜索
- 分类筛选：按产品分类进行筛选
- 价格范围：支持最小价格到最大价格的范围筛选
- 属性筛选：支持颜色、尺寸、品牌等属性筛选
- 多值筛选：属性支持多个值的组合筛选

### 4. 排序功能
支持以下排序字段：
- `price`：按价格排序
- `name`：按产品名称排序  
- `created_at`：按创建时间排序
- `popularity`：按受欢迎程度排序

### 5. 分页支持
- 支持标准分页参数
- 返回完整的分页信息
- 可自定义每页显示数量

## 注意事项

1. **搜索关键词验证**：搜索关键词不能包含反斜杠字符
2. **性能考虑**：建议设置合理的limit值，避免一次性返回过多数据
3. **属性筛选**：只有标记为可筛选的属性才会出现在filterable_attributes中
4. **价格显示**：所有价格都会根据当前货币设置进行格式化
5. **库存状态**：in_stock字段基于库存数量计算得出
6. **图片URL**：所有图片URL都是完整的绝对路径

## 使用建议

1. **首次加载**：建议先调用接口获取filterable_attributes，用于构建筛选UI
2. **搜索优化**：合理使用分类ID可以提高搜索性能和准确性
3. **缓存策略**：可以对filterable_attributes进行适当缓存
4. **错误处理**：注意处理搜索重定向和验证错误情况 