# 用户优惠券系统使用指南

## 概述

本系统扩展了Bagisto的原有优惠券功能，增加了向指定用户发放优惠券的能力。管理员可以将符合cart-rules规则的优惠券发放到指定用户账户中。

## 数据库结构

### customer_coupons 表字段说明

- `id`: 主键
- `customer_id`: 客户ID（外键关联customers表）
- `cart_rule_coupon_id`: 优惠券ID（外键关联cart_rule_coupons表）
- `coupon_code`: 优惠券代码（冗余存储，便于查询）
- `status`: 状态（active/used/expired/revoked）
- `usage_limit`: 该用户的使用次数限制
- `times_used`: 已使用次数
- `issued_at`: 发放时间
- `used_at`: 首次使用时间
- `expired_at`: 过期时间
- `issued_reason`: 发放原因/备注
- `issued_by`: 发放管理员ID

## 核心功能使用示例

### 1. 基础模型操作

```php
// 获取用户的所有优惠券
$customer = Customer::find(1);
$coupons = $customer->customer_coupons;

// 获取用户可用的优惠券
$availableCoupons = $customer->available_coupons;

// 检查优惠券状态
$customerCoupon = CustomerCoupon::find(1);
if ($customerCoupon->isAvailable()) {
    // 优惠券可用
}
```

### 2. Repository 使用示例

```php
// 注入Repository
$customerCouponRepo = app(CustomerCouponRepository::class);

// 向单个用户发放优惠券
$customerCoupon = $customerCouponRepo->issueCouponToCustomer(
    $customerId = 1,
    $cartRuleCouponId = 5,
    [
        'usage_limit' => 3,
        'expired_at' => '2024-12-31',
        'issued_reason' => '新用户欢迎礼包',
        'issued_by' => auth('admin')->id()
    ]
);

// 批量发放优惠券
$results = $customerCouponRepo->bulkIssueCoupons(
    [1, 2, 3, 4, 5], // 用户ID数组
    $cartRuleCouponId = 5,
    ['issued_reason' => '节日促销活动']
);

// 获取用户可用优惠券
$availableCoupons = $customerCouponRepo->getAvailableCouponsForCustomer(1);

// 验证并使用优惠券
$usedCoupon = $customerCouponRepo->validateAndUseCoupon(1, 'WELCOME2024');
```

### 3. Service 服务类使用

```php
// 注入Service
$couponService = app(CustomerCouponService::class);

// 根据购物车规则条件自动发放优惠券
$result = $couponService->distributeByCartRuleConditions(
    $cartRuleId = 1,
    $cartRuleCouponId = 5,
    [
        'exclude_existing' => true, // 排除已有优惠券的用户
        'limit' => 100,            // 限制发放数量
        'issued_reason' => '满足VIP条件自动发放'
    ]
);

// 向指定用户发放
$result = $couponService->distributeToCustomers(
    [1, 2, 3],
    $cartRuleCouponId = 5,
    ['issued_reason' => '客服手动发放']
);

// 获取发放统计
$stats = $couponService->getDistributionStats($cartRuleId = 1);
```

## 控制器实现建议

### 后台管理控制器

```php
<?php

namespace Webkul\Admin\Http\Controllers\Marketing\Promotions;

use Illuminate\Http\Request;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\CartRule\Services\CustomerCouponService;
use Webkul\CartRule\Repositories\CustomerCouponRepository;

class CustomerCouponController extends Controller
{
    public function __construct(
        protected CustomerCouponService $couponService,
        protected CustomerCouponRepository $customerCouponRepository
    ) {}

    /**
     * 显示用户优惠券列表
     */
    public function index()
    {
        // 实现DataGrid列表页面
    }

    /**
     * 发放优惠券页面
     */
    public function create()
    {
        // 显示发放表单
    }

    /**
     * 执行优惠券发放
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'cart_rule_coupon_id' => 'required|exists:cart_rule_coupons,id',
            'distribution_type' => 'required|in:manual,rule_based',
            'customer_ids' => 'required_if:distribution_type,manual|array',
            'cart_rule_id' => 'required_if:distribution_type,rule_based|exists:cart_rules,id',
            'usage_limit' => 'nullable|integer|min:1',
            'expired_at' => 'nullable|date|after:today',
            'issued_reason' => 'nullable|string|max:500'
        ]);

        try {
            if ($validated['distribution_type'] === 'manual') {
                // 手动指定用户发放
                $result = $this->couponService->distributeToCustomers(
                    $validated['customer_ids'],
                    $validated['cart_rule_coupon_id'],
                    $validated
                );
            } else {
                // 根据规则条件发放
                $result = $this->couponService->distributeByCartRuleConditions(
                    $validated['cart_rule_id'],
                    $validated['cart_rule_coupon_id'],
                    $validated
                );
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * 撤销优惠券
     */
    public function revoke(Request $request, int $id)
    {
        $validated = $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $success = $this->customerCouponRepository->revokeCoupon(
            $id,
            $validated['reason']
        );

        return response()->json([
            'success' => $success,
            'message' => $success ? '优惠券已撤销' : '撤销失败'
        ]);
    }

    /**
     * 获取发放统计
     */
    public function stats(Request $request)
    {
        $cartRuleId = $request->get('cart_rule_id');
        $stats = $this->couponService->getDistributionStats($cartRuleId);

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
```

### 前台用户查看控制器

```php
<?php

namespace Webkul\Shop\Http\Controllers\Customer\Account;

use Webkul\Shop\Http\Controllers\Controller;
use Webkul\CartRule\Repositories\CustomerCouponRepository;

class CouponController extends Controller
{
    public function __construct(
        protected CustomerCouponRepository $customerCouponRepository
    ) {}

    /**
     * 用户优惠券中心
     */
    public function index()
    {
        $customerId = auth('customer')->id();
        
        $availableCoupons = $this->customerCouponRepository
            ->getAvailableCouponsForCustomer($customerId);
            
        $allCoupons = $this->customerCouponRepository
            ->getCustomerCoupons($customerId);

        return view('shop::customers.account.coupons.index', compact(
            'availableCoupons',
            'allCoupons'
        ));
    }
}
```

## 定时任务设置

### 清理过期优惠券

```php
// 在 app/Console/Kernel.php 中添加
protected function schedule(Schedule $schedule)
{
    // 每天凌晨1点清理过期优惠券
    $schedule->call(function () {
        app(CustomerCouponService::class)->cleanupExpiredCoupons();
    })->dailyAt('01:00');
}
```

## 事件监听器

### 优惠券使用监听

```php
// 在CartRule的Order监听器中添加
public function handle($order)
{
    // 原有逻辑...
    
    // 处理用户优惠券使用
    if ($order->coupon_code && $order->customer_id) {
        $customerCouponRepo = app(CustomerCouponRepository::class);
        $customerCouponRepo->validateAndUseCoupon(
            $order->customer_id,
            $order->coupon_code
        );
    }
}
```

## API 路由建议

```php
// routes/api.php 或者包内路由文件
Route::prefix('admin')->middleware(['auth:admin'])->group(function () {
    Route::prefix('customer-coupons')->group(function () {
        Route::get('/', [CustomerCouponController::class, 'index']);
        Route::post('/distribute', [CustomerCouponController::class, 'store']);
        Route::put('/{id}/revoke', [CustomerCouponController::class, 'revoke']);
        Route::get('/stats', [CustomerCouponController::class, 'stats']);
    });
});

Route::prefix('customer')->middleware(['auth:customer'])->group(function () {
    Route::get('/coupons', [Customer\CouponController::class, 'index']);
});
```

## 数据库迁移运行

```bash
# 运行迁移创建customer_coupons表
php artisan migrate
```

## 注意事项

1. **数据一致性**: 确保在发放优惠券时检查原始优惠券的有效性
2. **性能优化**: 批量操作时使用数据库事务和批量插入
3. **权限控制**: 确保只有授权管理员可以发放和撤销优惠券
4. **日志记录**: 重要操作应记录详细日志便于审计
5. **缓存策略**: 用户优惠券查询频繁，考虑添加适当缓存

## 扩展建议

1. **高级筛选**: 可根据用户订单历史、消费金额等更复杂条件筛选
2. **模板系统**: 创建优惠券发放模板，便于重复使用
3. **通知系统**: 发放优惠券后自动通知用户
4. **分析报表**: 提供详细的优惠券使用分析报表
5. **导入导出**: 支持批量导入用户ID和导出发放记录 