<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Customer\Repositories\WishlistRepository;
use Webkul\MLKWebAPI\Http\Resources\ProductResource;

class BaseProductFormatter
{
    /**
     * CustomerRepository object
     *
     * @var \Webkul\Customer\Repositories\CustomerRepository
     */
    protected $customerRepository;

    /**
     * WishlistRepository object
     *
     * @var \Webkul\Customer\Repositories\WishlistRepository
     */
    protected $wishlistRepository;

    /**
     * 当前客户组ID缓存
     *
     * @var int|null
     */
    protected $currentCustomerGroupId = null;

    /**
     * 当前用户的wishlist产品ID缓存
     *
     * @var array|null
     */
    protected $currentUserWishlistProductIds = null;

    /**
     * Create a new instance.
     *
     * @param  \Webkul\Customer\Repositories\CustomerRepository  $customerRepository
     * @param  \Webkul\Customer\Repositories\WishlistRepository  $wishlistRepository
     * @return void
     */
    public function __construct(
        CustomerRepository $customerRepository,
        WishlistRepository $wishlistRepository
    ) {
        $this->customerRepository = $customerRepository;
        $this->wishlistRepository = $wishlistRepository;
    }

    /**
     * 获取当前客户组ID
     *
     * @return int
     */
    protected function getCurrentCustomerGroupId()
    {
        if ($this->currentCustomerGroupId === null) {
            // 获取当前登录用户的客户组ID，如果未登录则使用访客组
            $customerGroup = $this->customerRepository->getCurrentGroup();
            $this->currentCustomerGroupId = $customerGroup->id;
        }
        
        return $this->currentCustomerGroupId;
    }

    /**
     * 获取当前用户的wishlist产品ID列表
     *
     * @return array
     */
    protected function getCurrentUserWishlistProductIds()
    {
        if ($this->currentUserWishlistProductIds === null) {
            $this->currentUserWishlistProductIds = [];
            
            // 检查用户是否已登录
            $user = auth()->guard('sanctum')->user();
            if ($user) {
                try {
                    // 获取当前用户在当前渠道的wishlist产品ID
                    $wishlistItems = $this->wishlistRepository->findWhere([
                        'customer_id' => $user->id,
                        'channel_id' => core()->getCurrentChannel()->id,
                    ]);
                    
                    $this->currentUserWishlistProductIds = $wishlistItems->pluck('product_id')->toArray();
                } catch (\Exception $e) {
                    // 如果获取失败，记录错误但继续执行
                    logger()->warning('Failed to get user wishlist: ' . $e->getMessage());
                }
            }
        }
        
        return $this->currentUserWishlistProductIds;
    }

    // /**
    //  * 检查产品是否在当前用户的wishlist中
    //  *
    //  * @param int $productId
    //  * @return bool
    //  */
    // protected function isProductInWishlist($productId)
    // {
    //     $wishlistProductIds = $this->getCurrentUserWishlistProductIds();
    //     return in_array($productId, $wishlistProductIds);
    // }

    /**
     * 批量预加载产品价格相关数据以优化查询性能
     *
     * @param \Illuminate\Database\Eloquent\Collection $products
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function preloadProductPriceData($products)
    {
        // 预加载所有价格计算相关的关系，避免N+1查询
        return $products->load([
            'images',
            'attribute_values',
            'super_attributes.options',
            'variants.attribute_values',
            'variants.price_indices' => function ($query) {
                $query->where('channel_id', core()->getCurrentChannel()->id)
                      ->where('customer_group_id', $this->getCurrentCustomerGroupId());
            },
            'price_indices' => function ($query) {
                $query->where('channel_id', core()->getCurrentChannel()->id)
                      ->where('customer_group_id', $this->getCurrentCustomerGroupId());
            },
            'catalog_rule_prices' => function ($query) {
                $query->where('channel_id', core()->getCurrentChannel()->id)
                      ->where('customer_group_id', $this->getCurrentCustomerGroupId())
                      ->where('rule_date', now()->format('Y-m-d'));
            },
            'customer_group_prices' => function ($query) {
                $query->where('customer_group_id', $this->getCurrentCustomerGroupId())
                      ->orderBy('qty', 'asc');
            },
            'variants.catalog_rule_prices' => function ($query) {
                $query->where('channel_id', core()->getCurrentChannel()->id)
                      ->where('customer_group_id', $this->getCurrentCustomerGroupId())
                      ->where('rule_date', now()->format('Y-m-d'));
            },
            'variants.customer_group_prices' => function ($query) {
                $query->where('customer_group_id', $this->getCurrentCustomerGroupId())
                      ->orderBy('qty', 'asc');
            },
            'categories',
            'reviews',
            'videos',
            'attribute_family',
        ]);
    }

    /**
     * 格式化产品数据
     *
     * @param \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection $products
     * @return array
     */
    public function formatProducts($products)
    {
        // 如果是分页结果，先预加载数据然后使用Resource格式化
        if (method_exists($products, 'getCollection')) {
            // 这是一个分页对象
            $productCollection = $this->preloadProductPriceData($products->getCollection());
            
            // 使用ProductResource格式化每个产品
            $formattedProducts = $productCollection->map(function ($product) {
                return (new ProductResource($product))->toArray(request());
            })->toArray();
            
            return $formattedProducts;
        } else {
            // 这是一个普通集合
            $productCollection = $this->preloadProductPriceData($products);
            
            // 使用ProductResource格式化每个产品
            $formattedProducts = $productCollection->map(function ($product) {
                return (new ProductResource($product))->toArray(request());
            })->toArray();
            
            return $formattedProducts;
        }
    }
} 