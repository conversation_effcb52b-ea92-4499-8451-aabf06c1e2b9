# 产品 API 接口文档

## 概述
产品 API 提供了完整的产品信息查询功能，包括产品详情、相关产品、特价信息、设备品牌信息等。

## 基础信息
- **Base URL**: `/mlk/api/product`
- **Content-Type**: `application/json`
- **Authentication**: <PERSON><PERSON> (如需要)

---

## 1. 产品详情接口

### 接口信息
- **URL**: `POST /mlk/api/product/detail`
- **Method**: `POST`
- **Description**: 获取产品的详细信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 产品ID |

### 请求示例
```json
{
    "id": 123
}
```

### 响应格式

#### 成功响应 (200)
```json
{
    "success": true,
    "message": "成功",
    "data": {
        "product": {
            // ProductResource 数据结构
            "id": 123,
            "sku": "PROD-001",
            "name": "产品名称",
            "description": "产品描述",
            "short_description": "简短描述",
            "url_key": "product-url-key",
            "price": 100.00,
            "formatted_price": "$100.00",
            "special_price": 80.00,
            "images": [
                {
                    "id": 1,
                    "url": "https://example.com/image.jpg",
                    "path": "product/image.jpg",
                    "position": 1
                }
            ],
            "videos": [
                {
                    "id": 1,
                    "url": "https://youtube.com/watch?v=xxx",
                    "type": "youtube",
                    "position": 1
                }
            ],
            "categories": [
                {
                    "id": 1,
                    "name": "分类名称",
                    "url_key": "category-url-key"
                }
            ],
            "weight": 1.5,
            "status": true,
            "visible_individually": true,
            "meta_title": "SEO标题",
            "meta_description": "SEO描述",
            "meta_keywords": "SEO关键词",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        },
        "type": "simple",
        "is_saleable": true,
        "is_stockable": true,
        "total_quantity": 100,
        "show_quantity_box": true,
        "prices": {
            "regular_price": 100.00,
            "final_price": 80.00,
            "price_html": "$80.00"
        },
        "customer_group": {
            "id": 1,
            "name": "Default",
            "code": "default"
        },
        "variants": [
            {
                "id": 124,
                "sku": "PROD-001-RED",
                "name": "产品名称 - 红色",
                "price": 100.00,
                "formatted_price": "$100.00",
                "special_price": 80.00,
                "color": {
                    "id": 23,
                    "label": "红色",
                    "admin_name": "Red",
                    "swatch_value": "#FF0000",
                    "sort_order": 1
                },
                "images": [
                    {
                        "id": 2,
                        "url": "https://example.com/red-image.jpg",
                        "path": "product/red-image.jpg",
                        "position": 1
                    }
                ],
                "inventory": {
                    "qty": 50,
                    "is_in_stock": true
                }
            }
        ],
        "customizable_options": [
            {
                "id": 1,
                "type": "text",
                "label": "自定义文本",
                "is_required": true,
                "sort_order": 1,
                "prices": [
                    {
                        "id": 1,
                        "label": "额外费用",
                        "price": 10.00,
                        "formatted_price": "$10.00",
                        "price_type": "fixed",
                        "sort_order": 1
                    }
                ]
            }
        ],
        "downloadable_links": [
            {
                "id": 1,
                "title": "下载链接标题",
                "price": 5.00,
                "formatted_price": "$5.00",
                "downloads": 10,
                "sample_url": "https://example.com/sample.pdf",
                "sample_file_url": "https://example.com/sample-file.pdf",
                "sort_order": 1
            }
        ],
        "downloadable_samples": [
            {
                "id": 1,
                "title": "样本标题",
                "url": "https://example.com/sample.pdf",
                "file_url": "https://example.com/sample-file.pdf",
                "sort_order": 1
            }
        ],
        "bundle_options": [
            {
                "id": 1,
                "type": "select",
                "label": "选择产品",
                "is_required": true,
                "sort_order": 1,
                "products": [
                    {
                        "id": 1,
                        "product_id": 125,
                        "product_name": "套装产品名称",
                        "qty": 1,
                        "is_default": true,
                        "is_user_defined": false,
                        "sort_order": 1
                    }
                ]
            }
        ],
        "customer_group_prices": [
            {
                "qty": 1,
                "value_type": "fixed",
                "original_value": 90.00,
                "price": 90.00,
                "formatted_price": "$90.00"
            }
        ],
        "seo": {
            "meta_title": "SEO标题",
            "meta_description": "SEO描述",
            "meta_keywords": "SEO关键词",
            "url_key": "product-url-key"
        },
        "special_price": {
            "price": 80.00,
            "formatted_price": "$80.00",
            "regular_price": 100.00,
            "formatted_regular_price": "$100.00",
            "discount_percentage": 20.00,
            "start_date": "2024-01-01 00:00:00",
            "end_date": "2024-12-31 23:59:59",
            "start_date_formatted": "2024-01-01",
            "end_date_formatted": "2024-12-31",
            "is_active": true
        },
        "devices": [
            {
                "id": 123,
                "name": "iPhone 15 Pro",
                "sort_order": 1,
                "parent_id": 456
            }
        ],
        "brands": [
            {
                "id": 456,
                "name": "Apple",
                "sort_order": 1
            }
        ]
    }
}
```

#### 错误响应 (404)
```json
{
    "success": false,
    "message": "产品不存在"
}
```

#### 错误响应 (400)
```json
{
    "success": false,
    "message": "请求错误",
    "data": {
        "error": "具体错误信息"
    }
}
```

---

## 2. 相关产品接口

### 接口信息
- **URL**: `GET /mlk/api/product/{id}/related`
- **Method**: `GET`
- **Description**: 获取产品的相关产品列表

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 产品ID（路径参数） |

### 响应格式
```json
{
    "success": true,
    "message": "成功",
    "data": [
        {
            "id": 126,
            "sku": "RELATED-001",
            "name": "相关产品名称",
            "price": 120.00,
            "formatted_price": "$120.00",
            "special_price": 100.00,
            "images": [
                {
                    "id": 3,
                    "url": "https://example.com/related-image.jpg",
                    "path": "product/related-image.jpg",
                    "position": 1
                }
            ],
            "categories": [
                {
                    "id": 2,
                    "name": "相关分类",
                    "url_key": "related-category"
                }
            ],
            "url_key": "related-product-url-key"
        }
    ]
}
```

### 配置说明
- 返回的产品数量由系统配置 `catalog.products.product_view_page.no_of_related_products` 控制

---

## 3. 向上销售产品接口

### 接口信息
- **URL**: `GET api/mlk/product/{id}/up-sell`
- **Method**: `GET`
- **Description**: 获取产品的向上销售产品列表

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 产品ID（路径参数） |

### 响应格式
```json
{
    "success": true,
    "message": "成功",
    "data": [
        {
            "id": 127,
            "sku": "UPSELL-001",
            "name": "向上销售产品",
            "price": 150.00,
            "formatted_price": "$150.00",
            "special_price": 130.00,
            "images": [
                {
                    "id": 4,
                    "url": "https://example.com/upsell-image.jpg",
                    "path": "product/upsell-image.jpg",
                    "position": 1
                }
            ],
            "categories": [
                {
                    "id": 3,
                    "name": "高端分类",
                    "url_key": "premium-category"
                }
            ],
            "url_key": "upsell-product-url-key"
        }
    ]
}
```

### 配置说明
- 返回的产品数量由系统配置 `catalog.products.product_view_page.no_of_up_sells_products` 控制

---

## 4. 交叉销售产品接口

### 接口信息
- **URL**: `GET /api/mlk/product/{id}/cross-sell`
- **Method**: `GET`
- **Description**: 获取产品的交叉销售产品列表

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 产品ID（路径参数） |

### 响应格式
```json
{
    "success": true,
    "message": "成功",
    "data": [
        {
            "id": 128,
            "sku": "CROSS-001",
            "name": "交叉销售产品",
            "price": 50.00,
            "formatted_price": "$50.00",
            "special_price": 45.00,
            "images": [
                {
                    "id": 5,
                    "url": "https://example.com/cross-image.jpg",
                    "path": "product/cross-image.jpg",
                    "position": 1
                }
            ],
            "categories": [
                {
                    "id": 4,
                    "name": "配件分类",
                    "url_key": "accessory-category"
                }
            ],
            "url_key": "cross-sell-product-url-key"
        }
    ]
}
```

### 配置说明
- 返回的产品数量由系统配置 `catalog.products.product_view_page.no_of_cross_sells_products` 控制，默认为4个

---

## 5. 记录访问接口

### 接口信息
- **URL**: `POST /api/products/record-visit`
- **Method**: `POST`
- **Description**: 记录产品访问统计

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | integer | 是 | 产品ID |

### 请求示例
```json
{
    "product_id": 123
}
```

### 响应格式
```json
{
    "success": true,
    "message": "产品访问记录成功",
    "data": {
        "recorded": true
    }
}
```

---

## 数据结构说明

### 产品类型 (type)
- `simple`: 简单产品
- `configurable`: 可配置产品
- `bundle`: 套装产品
- `downloadable`: 可下载产品
- `virtual`: 虚拟产品
- `grouped`: 分组产品
- `booking`: 预订产品

### 特价信息 (special_price)
- 只有当产品设置了特价时才会返回此字段
- `discount_percentage`: 自动计算的折扣百分比
- `is_active`: 基于当前时间判断特价是否有效

### 设备品牌信息 (devices/brands)
- 支持多个设备和品牌
- 设备与品牌通过 `parent_id` 关联
- 如果产品没有设备信息，则不返回相关字段

### 客户组价格 (customer_group_prices)
- 只有在启用自定义定价且存在客户组时才返回
- 支持固定价格和折扣类型

### 可定制选项 (customizable_options)
- 只有简单产品或虚拟产品可能有此字段
- 支持多种类型：text、textarea、select、radio、checkbox等

### 下载内容 (downloadable_links/downloadable_samples)
- 只有可下载产品才有此字段
- 包含下载链接和样本文件

### 套装选项 (bundle_options)
- 只有套装产品才有此字段
- 包含套装中的产品选项

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 产品不存在或不可见 |
| 500 | 服务器内部错误 |

---

## 注意事项

1. **产品可见性**: 只有 `visible_individually` 为 `true` 且 `status` 为 `true` 的产品才能访问
2. **URL Key**: 产品必须有有效的 `url_key` 才能被访问
3. **价格格式**: 所有价格字段都提供原始数值和格式化字符串两种格式
4. **图片路径**: 图片同时提供完整URL和相对路径
5. **颜色属性**: variants中的color字段包含颜色ID、标签、管理名称、色值(swatch_value)和排序信息
6. **异常处理**: 所有接口都包含完善的异常处理，确保API稳定性
7. **性能优化**: 使用了预加载来减少数据库查询次数

---

## 更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0 | 2024-01-01 | 初始版本 |
| 1.1 | 2024-01-01 | 添加特价信息支持 |
| 1.2 | 2024-01-01 | 添加设备品牌信息支持 |
| 1.3 | 2024-01-01 | 移除 attribute_values 和 super_attributes 字段 |
| 1.4 | 2024-01-01 | 统一相关产品接口实现，与Shop模块保持一致，添加配置控制返回数量 |
| 1.5 | 2024-01-01 | 在 variants 字段中添加 color 属性信息 | 