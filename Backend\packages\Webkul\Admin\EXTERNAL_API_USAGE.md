# 外部API接口使用说明

## 接口概述

该接口用于调用外部第三方API获取库存数据搜索结果。接口会自动处理登录认证和Session管理，无需手动传递Cookie信息。

## 接口地址

```
POST /admin/external-api/grid-data-search
```

## 环境变量配置

在项目根目录的 `.env` 文件中添加以下配置：

```bash
# 外部API登录配置
OUHUA_LOGIN_URL=http://*************:5001/api/login
OUHUA_USERNAME=your_username
OUHUA_PASSWORD=your_password
```

| 环境变量 | 说明 | 示例 |
|---------|------|------|
| OUHUA_LOGIN_URL | 外部系统登录接口地址 | http://*************:5001/api/login |
| OUHUA_USERNAME | 登录用户名 | dengchao2018 |
| OUHUA_PASSWORD | 登录密码 | your_password |

## 请求参数

### 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| key | string | 搜索关键字 | "1002NE IP17 6.6" |

### 可选参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| start | integer | 0 | 分页起始位置 |
| limit | integer | 25 | 每页记录数（最大100） |

## 请求示例

### cURL 请求

```bash
curl -X POST 'http://yourdomain.com/admin/external-api/grid-data-search' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_API_TOKEN' \
  -d '{
    "key": "1002NE IP17 6.6",
    "start": 0,
    "limit": 25
  }'
```

### JavaScript 请求

```javascript
fetch('/admin/external-api/grid-data-search', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({
        key: '1002NE IP17 6.6',
        start: 0,
        limit: 25
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('数据获取成功:', data.data);
    } else {
        console.error('请求失败:', data.message);
    }
})
.catch(error => {
    console.error('网络错误:', error);
});
```

## 响应格式

### 成功响应

```json
{
    "success": true,
    "data": {
        // 外部API返回的原始数据
    },
    "message": "数据获取成功"
}
```

### 失败响应

```json
{
    "success": false,
    "message": "错误描述",
    "error": "详细错误信息"
}
```

## 固定参数说明

以下参数是固定值，基于原始cURL请求：

- `leixing`: "fenlei"
- `FNodeId`: "20"
- `GNodeId`: "0"
- `ck`: ""
- `ziduan`: "Code"
- `jisuanfu`: "dengyu"
- `paixv`: ""
- `fangxiang`: "ASC"
- `IIsDate`: "false"
- `StartDate`: ""
- `EndDate`: ""

## 自动登录机制

### Session管理
- 系统会自动处理登录认证，获取PHPSESSID
- Session ID会被缓存1小时，避免频繁登录
- 当Session过期时，系统会自动重新登录并更新缓存
- 支持失败重试机制

### 登录流程
1. 首次调用时，系统使用配置的用户名密码登录外部系统
2. 解析登录响应，提取PHPSESSID并缓存
3. 后续请求使用缓存的Session ID
4. 如果请求返回401/403错误，自动清除缓存并重新登录

### 登录接口格式
登录接口需要接收以下请求：
```json
{
    "username": "your_username",
    "password": "your_password"
}
```

期望返回格式：
```json
{
    "cookies": {
        "PHPSESSID": "session_id_here"
    },
    "message": "登录成功",
    "success": true
}
```

## 错误处理

接口包含完整的错误处理机制：

1. **参数验证错误**：返回422状态码和验证错误信息
2. **登录失败错误**：返回500状态码，包含登录失败信息
3. **外部API调用失败**：记录错误日志并返回相应状态码
4. **Session过期处理**：自动重新登录并重试请求
5. **网络异常**：返回500状态码和异常信息

## 安全注意事项

1. 该接口需要管理员权限才能访问
2. 请妥善保管环境变量中的用户名和密码信息
3. 建议在生产环境中添加访问频率限制
4. 外部API调用使用了 `verify=false` 选项，请确保网络环境安全
5. Session ID会被缓存，确保缓存系统的安全性

## 技术实现

- 使用Laravel HTTP Client进行外部API调用
- 完整保留原始cURL请求的所有Headers
- 支持超时设置（30秒）
- 完整的错误日志记录
- 参数验证和数据清理
- 使用Laravel Cache系统进行Session缓存
- 自动登录和Session过期处理
- 失败重试机制 