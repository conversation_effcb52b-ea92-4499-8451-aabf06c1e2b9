# 电子产品配件数据种子 - 使用说明

## 概述

本数据种子文件为Bagisto电商系统提供完整的电子产品配件测试数据，包含60个产品，覆盖6个主要分类。

## 产品分布

### 产品分类和数量
- **PHONE HOLDERS（手机支架）**: 10个产品 (PH-001 到 PH-010)
- **CABLES（数据线）**: 10个产品 (CB-001 到 CB-010)  
- **POWER（电源）**: 10个产品 (PW-001 到 PW-010)
- **AUDIO（音频）**: 10个产品 (AU-001 到 AU-010)
- **ACCESSORIES（配件）**: 10个产品 (AC-001 到 AC-010)
- **Case（手机壳）**: 10个产品 (CS-001 到 CS-010)

### 语言版本
- **所有60个产品**: 英文版本

### 图片配置
- **手机壳产品**: 使用专用图片 `product/case/1.jpg` 到 `product/case/10.jpg`
- **其他所有产品**: 使用默认图片 `product/default.jpg`

### 产品示例（全英文版本）
- **手机支架类**: Magnetic Car Phone Mount, Desktop Phone Stand, Adjustable Laptop Stand
- **数据线类**: Type-C Fast Charging Cable, Lightning Charging Cable, HDMI Cable 4K
- **电源类**: 65W GaN Fast Charger, 20000mAh Power Bank, Wireless Charging Pad
- **音频类**: True Wireless Bluetooth Earbuds, Bluetooth Speaker, Gaming Headset
- **配件类**: Phone Ring Holder, Screen Protector Kit, Phone Cleaning Kit
- **手机壳类**: Liquid Silicone Phone Case, Crystal Clear Phone Case, Rugged Armor Phone Case

## 运行方法

### ⚠️ 重要提醒：运行索引以确保后台可见

**插入数据后必须运行索引同步，否则后台无法看到产品！**

```bash
# 方法1：同步flat索引（推荐）
php artisan indexer:index --type=flat

# 方法2：运行完整索引器
php artisan indexer:index
```

**原因说明：**
- Bagisto后台产品列表查询的是`product_flat`表
- 数据种子文件只插入了`products`表及相关表
- 必须运行索引命令将数据同步到`product_flat`表
- 这是Bagisto提高查询性能的设计机制

### 完整运行流程（推荐）

**Windows用户：**
```cmd
# 1. 运行数据种子
php artisan db:seed --class=Database\Seeders\ElectronicsProductSeeder

# 2. 同步索引（必须步骤）
php artisan indexer:index --type=flat

# 3. 清理缓存（可选）
php artisan cache:clear
```

**Linux/Mac用户：**
```bash
# 1. 运行数据种子
php artisan db:seed --class=Database\\Seeders\\ElectronicsProductSeeder

# 2. 同步索引（必须步骤）
php artisan indexer:index --type=flat

# 3. 清理缓存（可选）
php artisan cache:clear
```

### 方法1：使用快速运行脚本（推荐）

**Windows用户：**
```cmd
run_product_seeder.bat
```

**Linux/Mac用户：**
```bash
# 首先设置执行权限
chmod +x run_product_seeder.sh
# 然后运行
./run_product_seeder.sh
```

快速运行脚本提供交互式菜单，可以选择插入或清理产品数据。

### 方法2：单独运行产品数据种子文件
```bash
php artisan db:seed --class=Database\\Seeders\\ElectronicsProductSeeder
```

### 方法3：运行完整数据库种子（包含所有数据）
```bash
php artisan db:seed
```

### 方法4：重置数据库并运行种子文件
```bash
php artisan migrate:fresh --seed
```

## 前置条件

在运行产品数据种子文件之前，请确保：

1. **分类数据已存在**：您提到已经手动创建了分类数据，请确保以下分类存在于数据库中：
   - PHONE HOLDERS
   - CABLES
   - POWER
   - AUDIO
   - ACCESSORIES
   - Case

2. **基础系统数据完整**：
   - 属性族 (attribute_families)
   - 属性 (attributes)
   - 客户组 (customer_groups)
   - 渠道 (channels)
   - 库存来源 (inventory_sources)

## 插入的数据表

该seeder会向以下数据表插入数据：

- `products` - 产品基础信息
- `product_attribute_values` - 产品属性值
- `product_categories` - 产品分类关联
- `product_inventories` - 产品库存
- `product_inventory_indices` - 产品库存索引
- `product_price_indices` - 产品价格索引
- `product_channels` - 产品渠道关联
- `product_images` - 产品图片（仅手机壳类别）

## 产品图片

本版本特别为手机壳类产品配置了图片数据：
- 手机壳产品使用 `product/case/1.jpg` 到 `product/case/10.jpg` 图片
- 图片按照产品顺序依次分配 (CS-001对应1.jpg, CS-002对应2.jpg, 以此类推)
- 其他类别的产品可以根据需要在数据数组中添加 `image_path` 字段

### 创建图片占位符

如果您没有实际的产品图片，可以运行占位符生成脚本：

```bash
php create_image_placeholders.php
```

该脚本会在 `public/storage/product/case/` 目录下创建10个图片占位符文件，每个文件都标注了对应的产品名称。

## 产品属性

每个产品包含以下属性：
- 名称 (name)
- 简短描述 (short_description)
- 详细描述 (description)
- SKU编码 (sku)
- 价格 (price)
- 重量 (weight)
- 状态 (status)
- 特色产品标记 (featured)
- URL键 (url_key)
- SEO相关属性 (meta_title, meta_description)

## 库存信息

- 所有产品默认设置为有库存状态
- 库存数量根据产品类型设置（50-600件不等）
- 使用默认库存来源 (inventory_source_id = 1)

## 注意事项

1. 运行前请备份数据库，避免数据丢失
2. 如果分类名称与您的实际分类不匹配，请修改seeder文件中的 `category_name` 字段
3. 价格单位为人民币元，可根据需要调整
4. 手机壳产品已配置图片路径（product/case/1-10.jpg），请确保图片文件存在于相应路径
5. 产品名称和描述采用英文版本，特别是手机壳类产品

## 自定义修改

如需修改产品数据，请编辑 `database/seeders/ElectronicsProductSeeder.php` 文件中的 `$products` 数组。

## 清理数据

如果需要清理这些测试产品数据，可以运行清理脚本：

```bash
php artisan db:seed --class=Database\\Seeders\\CleanElectronicsProductSeeder
```

该脚本会安全地清理所有相关的产品数据，包括：
- 产品基础信息
- 产品属性值
- 产品分类关联
- 产品库存和价格索引
- 产品图片和评论（如果存在）
- 产品关联关系

**注意**：清理操作不可逆，运行前请确保备份重要数据。

## 错误排查

如果运行时出现错误，请检查：
1. 分类数据是否存在
2. 数据库连接是否正常
3. 必要的系统基础数据是否完整
4. PHP内存限制是否足够

运行成功后，您可以在Bagisto后台的产品管理页面查看新添加的产品数据。 