<?php

namespace Webkul\Admin\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExternalApiController extends Controller
{
    /**
     * 外部API配置常量
     */
    private const CACHE_KEY = 'ouhua_phpsessid';
    private const CACHE_TTL = 3600; // 1小时
    private const API_BASE_URL = 'http://w3.ouhuasoft.com';
    private const SEARCH_ENDPOINT = '/kcxx_ico/GetGridDataForSearch.php';
    private const DEFAULT_TIMEOUT = 60;
    private const LOGIN_TIMEOUT = 30;
    private const MAX_LOG_BODY_LENGTH = 200;

    /**
     * HTTP请求头模板
     */
    private const DEFAULT_HEADERS = [
        'Accept' => '*/*',
        'Accept-Language' => 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control' => 'no-cache',
        'Connection' => 'keep-alive',
        'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin' => self::API_BASE_URL,
        'Pragma' => 'no-cache',
        'Referer' => self::API_BASE_URL . '/kcxx_ico/',
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'X-Requested-With' => 'XMLHttpRequest',
    ];

    /**
     * 搜索查询默认参数
     */
    private const SEARCH_QUERY_DEFAULTS = [
        'leixing' => 'fenlei',
        'FNodeId' => '0',
        'GNodeId' => '0',
        'ck' => '',
        'ziduan' => 'Code',
        'jisuanfu' => 'dengyu',
        'paixv' => '',
        'fangxiang' => 'ASC',
        'IIsDate' => 'false',
        'StartDate' => '',
        'EndDate' => '',
    ];

    /**
     * 获取网格数据搜索结果
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getGridDataForSearch(Request $request): JsonResponse
    {
        // 验证输入参数
        $validated = $request->validate([
            'key' => 'required|string|max:255',
            'start' => 'integer|min:0',
            'limit' => 'integer|min:1|max:100',
        ]);

        try {
            $sessionId = $this->getValidSessionId();
            
            if (!$sessionId) {
                return $this->errorResponse('无法获取有效的登录会话', '登录失败', 500);
            }

            $url = $this->buildSearchUrl($validated);
            $postData = $this->buildPostData($validated);

            // 尝试主请求
            $response = $this->makeApiRequest($url, $postData, $sessionId);
            
            if ($response->successful()) {
                return $this->handleSuccessfulResponse($response);
            }

            // 如果失败且是认证问题，尝试重新登录并重试
            if (in_array($response->status(), [401, 403])) {
                return $this->retryWithNewSession($url, $postData, $sessionId);
            }

            return $this->handleFailedResponse($response);

        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * 构建搜索URL
     *
     * @param array $validated
     * @return string
     */
    private function buildSearchUrl(array $validated): string
    {
        $queryParams = array_merge(self::SEARCH_QUERY_DEFAULTS, [
            'key' => $validated['key'],
        ]);

        $queryString = http_build_query($queryParams, '', '&', PHP_QUERY_RFC3986);
        
        return self::API_BASE_URL . self::SEARCH_ENDPOINT . '?' . $queryString;
    }

    /**
     * 构建POST数据
     *
     * @param array $validated
     * @return array
     */
    private function buildPostData(array $validated): array
    {
        return [
            'start' => $validated['start'] ?? 0,
            'limit' => $validated['limit'] ?? 25,
        ];
    }

    /**
     * 发起API请求
     *
     * @param string $url
     * @param array $postData
     * @param string $sessionId
     * @return \Illuminate\Http\Client\Response
     */
    private function makeApiRequest(string $url, array $postData, string $sessionId)
    {
        $headers = array_merge(self::DEFAULT_HEADERS, [
            'Cookie' => $this->buildCookieString($sessionId),
        ]);

        return Http::withHeaders($headers)
            ->withOptions(['verify' => false])
            ->timeout(self::DEFAULT_TIMEOUT)
            ->asForm()
            ->post($url, $postData);
    }

    /**
     * 构建Cookie字符串
     *
     * @param string $sessionId
     * @return string
     */
    private function buildCookieString(string $sessionId): string
    {
        $username = env('OUHUA_USERNAME', '');
        
        return "PHPSESSID={$sessionId}; JS_Node=1; Cid=OH000611; YUser={$username}; admin=1";
    }

    /**
     * 处理成功响应
     *
     * @param \Illuminate\Http\Client\Response $response
     * @return JsonResponse
     */
    private function handleSuccessfulResponse($response): JsonResponse
    {
        $jsonData = $this->parseJsonResponse($response);
        
        if ($jsonData === null) {
            return $this->errorResponse('JSON解析失败', 'JSON格式错误', 500);
        }

        return $this->successResponse($jsonData, '数据获取成功');
    }

    /**
     * 使用新会话重试请求
     *
     * @param string $url
     * @param array $postData
     * @param string $oldSessionId
     * @return JsonResponse
     */
    private function retryWithNewSession(string $url, array $postData, string $oldSessionId): JsonResponse
    {
        Cache::forget(self::CACHE_KEY);
        
        $newSessionId = $this->getValidSessionId();
        
        if (!$newSessionId || $newSessionId === $oldSessionId) {
            Log::warning('重试获取会话失败', [
                'old_session' => $oldSessionId,
                'new_session' => $newSessionId,
            ]);
            return $this->errorResponse('会话重新获取失败', '认证失败', 401);
        }

        $retryResponse = $this->makeApiRequest($url, $postData, $newSessionId);

        if ($retryResponse->successful()) {
            return $this->handleSuccessfulResponse($retryResponse);
        }

        return $this->handleFailedResponse($retryResponse, true);
    }

    /**
     * 处理失败响应
     *
     * @param \Illuminate\Http\Client\Response $response
     * @param bool $isRetry
     * @return JsonResponse
     */
    private function handleFailedResponse($response, bool $isRetry = false): JsonResponse
    {
        $prefix = $isRetry ? '重试请求失败' : '外部API调用失败';
        
        Log::error($prefix, [
            'status' => $response->status(),
            'body' => substr($response->body(), 0, self::MAX_LOG_BODY_LENGTH),
        ]);

        return $this->errorResponse(
            $prefix,
            '服务器返回状态: ' . $response->status(),
            $response->status()
        );
    }

    /**
     * 处理异常
     *
     * @param \Exception $e
     * @return JsonResponse
     */
    private function handleException(\Exception $e): JsonResponse
    {
        Log::error('外部API调用异常', [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
        ]);

        return $this->errorResponse('请求处理失败', $e->getMessage(), 500);
    }

    /**
     * 解析JSON响应（处理BOM字符）
     *
     * @param \Illuminate\Http\Client\Response $response
     * @return array|null
     */
    private function parseJsonResponse($response): ?array
    {
        $responseBody = $response->body();
        
        // 移除BOM字符（UTF-8 BOM: EF BB BF 或 Unicode BOM: FEFF）
        $cleanBody = preg_replace('/^\x{FEFF}/u', '', $responseBody);
        
        $jsonData = json_decode($cleanBody, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('JSON解析失败', [
                'error' => json_last_error_msg(),
                'raw_body' => substr($responseBody, 0, self::MAX_LOG_BODY_LENGTH),
            ]);
            
            return null;
        }

        return $jsonData;
    }

    /**
     * 获取有效的Session ID
     *
     * @return string|null
     */
    private function getValidSessionId(): ?string
    {
        // 先尝试从缓存获取
        $sessionId = Cache::get(self::CACHE_KEY);
        
        if ($sessionId) {
            return $sessionId;
        }

        // 缓存中没有，尝试登录获取新的session
        return $this->performLogin();
    }

    /**
     * 执行登录获取Session ID
     *
     * @return string|null
     */
    private function performLogin(): ?string
    {
        $credentials = $this->getLoginCredentials();
        
        if (!$credentials) {
            return null;
        }

        try {
            Log::info('尝试登录获取Session', [
                'url' => $credentials['url'], 
                'username' => $credentials['username']
            ]);

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => self::DEFAULT_HEADERS['User-Agent'],
            ])
            ->withOptions(['verify' => false])
            ->timeout(self::LOGIN_TIMEOUT)
            ->post($credentials['url'], [
                'username' => $credentials['username'],
                'password' => $credentials['password'],
            ]);

            return $this->handleLoginResponse($response);

        } catch (\Exception $e) {
            Log::error('登录过程发生异常', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
            
            return null;
        }
    }

    /**
     * 获取登录凭据
     *
     * @return array|null
     */
    private function getLoginCredentials(): ?array
    {
        $loginUrl = env('OUHUA_LOGIN_URL');
        $username = env('OUHUA_USERNAME');
        $password = env('OUHUA_PASSWORD');

        if (!$loginUrl || !$username || !$password) {
            Log::error('缺少登录配置', [
                'login_url' => $loginUrl ? '已配置' : '未配置',
                'username' => $username ? '已配置' : '未配置',
                'password' => $password ? '已配置' : '未配置',
            ]);
            
            return null;
        }

        return [
            'url' => $loginUrl,
            'username' => $username,
            'password' => $password,
        ];
    }

    /**
     * 处理登录响应
     *
     * @param \Illuminate\Http\Client\Response $response
     * @return string|null
     */
    private function handleLoginResponse($response): ?string
    {
        if (!$response->successful()) {
            Log::error('登录请求失败', [
                'status' => $response->status(),
                'body' => substr($response->body(), 0, self::MAX_LOG_BODY_LENGTH),
            ]);
            
            return null;
        }

        $data = $response->json();
        
        Log::info('登录响应', ['data' => $data]);

        if (!isset($data['success']) || !$data['success'] || !isset($data['cookies']['PHPSESSID'])) {
            Log::error('登录响应格式不正确', ['response' => $data]);
            return null;
        }

        $sessionId = $data['cookies']['PHPSESSID'];
        
        // 将Session ID缓存
        Cache::put(self::CACHE_KEY, $sessionId, self::CACHE_TTL);
        
        Log::info('登录成功，Session ID已缓存', ['session_id' => $sessionId]);
        
        return $sessionId;
    }

    /**
     * 返回成功响应
     *
     * @param array $data
     * @param string $message
     * @return JsonResponse
     */
    private function successResponse(array $data, string $message = '操作成功'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
        ]);
    }

    /**
     * 返回错误响应
     *
     * @param string $message
     * @param string $error
     * @param int $status
     * @return JsonResponse
     */
    private function errorResponse(string $message, string $error, int $status = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => $error,
        ], $status);
    }
}