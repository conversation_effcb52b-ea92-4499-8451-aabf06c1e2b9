# 用户头像和个人资料管理 API 文档

## 接口概述

该模块提供了用户头像上传、修改昵称等个人资料管理功能。所有接口都需要用户登录认证。

## 基础信息

- **前缀路径**: `/api/mlk/dashboard`
- **认证方式**: <PERSON><PERSON> (Sanctum)
- **响应格式**: JSON

## API 接口列表

### 1. 获取用户信息

**接口**: `GET /api/mlk/dashboard/profile`

**描述**: 获取当前登录用户的详细信息

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "customer": {
            "id": 1,
            "first_name": "张",
            "last_name": "三",
            "name": "张 三",
            "email": "<EMAIL>",
            "phone": "13800138000",
            "image": "customer/avatars/12345.jpg",
            "image_url": "http://example.com/storage/customer/avatars/12345.jpg",
            "gender": "male",
            "date_of_birth": "1990-01-01",
            "status": 1,
            "group": {
                "id": 2,
                "name": "普通用户"
            },
            "profile": {
                "social_title": "先生",
                "city": "北京",
                "country": "中国"
            }
        }
    }
}
```

### 2. 更新用户头像

**接口**: `POST /api/mlk/dashboard/update-avatar`

**描述**: 上传并更新用户头像

**请求头**:
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数**:
- `avatar` (file, required): 头像图片文件
  - 支持格式: jpeg, jpg, png, webp
  - 最大文件大小: 2MB

**响应示例**:
```json
{
    "success": true,
    "message": "头像更新成功",
    "data": {
        "customer": {
            "id": 1,
            "first_name": "张",
            "last_name": "三",
            "image": "customer/avatars/new_avatar.jpg",
            "image_url": "http://example.com/storage/customer/avatars/new_avatar.jpg"
        },
        "avatar_url": "http://example.com/storage/customer/avatars/new_avatar.jpg"
    }
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "图片文件不能超过2MB"
}
```

### 3. 删除用户头像

**接口**: `POST /api/mlk/dashboard/delete-avatar`

**描述**: 删除用户当前头像

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**响应示例**:
```json
{
    "success": true,
    "message": "头像删除成功",
    "data": {
        "customer": {
            "id": 1,
            "first_name": "张",
            "last_name": "三",
            "image": null,
            "image_url": null
        }
    }
}
```

### 4. 更新用户基本信息

**接口**: `POST /api/mlk/dashboard/update-profile`

**描述**: 更新用户姓名等基本信息

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数**:
```json
{
    "first_name": "李",
    "last_name": "四"
}
```

**字段说明**:
- `first_name` (string, required): 名字，最大255字符
- `last_name` (string, required): 姓氏，最大255字符

**响应示例**:
```json
{
    "success": true,
    "message": "个人信息更新成功",
    "data": {
        "customer": {
            "id": 1,
            "first_name": "李",
            "last_name": "四",
            "name": "李 四",
            "email": "<EMAIL>"
        }
    }
}
```

## 错误处理

### 常见错误码

- **401 Unauthorized**: 未登录或Token无效
- **422 Validation Error**: 请求参数验证失败
- **500 Internal Server Error**: 服务器内部错误

### 错误响应格式

```json
{
    "success": false,
    "message": "错误描述信息"
}
```

## 使用示例

### JavaScript (使用 fetch)

```javascript
// 获取用户信息
async function getUserProfile() {
    const response = await fetch('/api/mlk/dashboard/profile', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json'
        }
    });
    return await response.json();
}

// 更新头像
async function updateAvatar(file) {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await fetch('/api/mlk/dashboard/update-avatar', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${userToken}`
        },
        body: formData
    });
    return await response.json();
}

// 更新用户信息
async function updateProfile(firstName, lastName) {
    const response = await fetch('/api/mlk/dashboard/update-profile', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            first_name: firstName,
            last_name: lastName
        })
    });
    return await response.json();
}
```

### cURL 示例

```bash
# 获取用户信息
curl -X GET "http://localhost/api/mlk/dashboard/profile" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 更新头像
curl -X POST "http://localhost/api/mlk/dashboard/update-avatar" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "avatar=@/path/to/avatar.jpg"

# 更新用户信息
curl -X POST "http://localhost/api/mlk/dashboard/update-profile" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "李",
    "last_name": "四"
  }'
```

## 注意事项

1. **文件上传**: 头像文件会存储在 `storage/app/public/customer/avatars/` 目录下
2. **文件大小**: 头像文件最大支持2MB
3. **文件格式**: 仅支持 jpeg, jpg, png, webp 格式
4. **权限验证**: 所有接口都需要有效的认证Token
5. **文件清理**: 更新头像时会自动删除旧的头像文件
6. **存储链接**: 确保执行了 `php artisan storage:link` 命令以创建符号链接

## 相关路由

所有路由都注册在 `packages/Webkul/MLKWebAPI/src/Routes/api.php` 文件中：

- `mlk.api.dashboard.profile` - 获取用户信息
- `mlk.api.dashboard.update_avatar` - 更新头像
- `mlk.api.dashboard.delete_avatar` - 删除头像  
- `mlk.api.dashboard.update_profile` - 更新基本信息 