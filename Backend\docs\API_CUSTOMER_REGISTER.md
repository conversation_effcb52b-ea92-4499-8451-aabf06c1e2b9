# 用户注册接口 API 文档

## 接口概述

**接口地址：** `POST /api/customer/register`

**功能描述：** 用户注册接口，支持零售用户和批发用户的注册，可以收集不同类型用户的详细信息。

**认证要求：** 无需认证

---

## 请求参数

### 基础必填参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `first_name` | string | 是 | 名字 |
| `last_name` | string | 是 | 姓氏 |
| `email` | string | 是 | 邮箱地址（必须唯一） |
| `password` | string | 是 | 密码（最少6位） |
| `phone` | string | 是 | 手机号码（必须唯一） |
| `user_type` | string | 是 | 用户类型：`general`（零售）、`wholesale`（批发）、`guest`（访客） |

### 通用可选参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `social_title` | string | 否 | 称谓（Mr./Mrs./Ms.） |
| `date_of_birth` | date | 否 | 出生日期（格式：YYYY-MM-DD） |
| `gender` | string | 否 | 性别 |
| `zip_postal_code` | string | 否 | 邮政编码 |
| `city` | string | 否 | 城市 |
| `state` | string | 否 | 州/省 |
| `country` | string | 否 | 国家 |

### 批发用户专用参数

当 `user_type` 为 `wholesale` 时，可以提供以下额外参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `vat_number` | string | 否 | 税号 |
| `identification_number` | string | 否 | 身份证号 |
| `pec` | string | 否 | 认证邮件地址 |
| `recipient_code` | string | 否 | 收件人代码 |
| `address` | string | 否 | 详细地址 |
| `company` | string | 否 | 公司名称 |
| `invoice_type` | string | 否 | 发票类型 |
| `activity_types` | array | 否 | 业务活动类型数组 |
| `how_know_us` | array | 否 | 了解我们的渠道数组 |

#### 业务活动类型选项 (`activity_types`)

- `"Wholesaler of telephone accessories"`（电话配件批发商）
- `"Smartphone wholesaler"`（智能手机批发商）
- `"Repair Center"`（维修中心）
- `"Bar/Tobacconist"`（酒吧/烟草商）
- `"Retailer of telephone accessories"`（电话配件零售商）
- `"Smartphone retailer"`（智能手机零售商）
- `"Stationery/Copy shop"`（文具/复印店）

#### 了解渠道选项 (`how_know_us`)

- `"Agent"`（代理）
- `"Facebook"`
- `"Amazon"`
- `"Web search"`（网络搜索）
- `"Instagram"`
- `"TikTok"`
- `"LinkedIn"`
- `"Pinterest"`
- `"Heard about it"`（听说）

---

## 请求示例

### 零售用户注册

```json
{
  "first_name": "张",
  "last_name": "三",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "13800138000",
  "user_type": "general",
  "social_title": "Mr.",
  "date_of_birth": "1990-05-15",
  "gender": "male",
  "zip_postal_code": "100000",
  "city": "北京",
  "state": "北京市",
  "country": "中国"
}
```

### 批发用户注册

```json
{
  "first_name": "李",
  "last_name": "四",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "13800138001",
  "user_type": "wholesale",
  "social_title": "Mr.",
  "date_of_birth": "1985-03-20",
  "gender": "male",
  "company": "李氏贸易有限公司",
  "vat_number": "*********",
  "identification_number": "110101198503201234",
  "pec": "<EMAIL>",
  "recipient_code": "ABC123",
  "address": "北京市朝阳区建国门外大街1号",
  "zip_postal_code": "100020",
  "city": "北京",
  "state": "北京市",
  "country": "中国",
  "invoice_type": "Invoice for company",
  "activity_types": [
    "Smartphone wholesaler",
    "Repair Center"
  ],
  "how_know_us": [
    "Web search",
    "Facebook"
  ]
}
```

---

## 响应格式

### 成功响应

#### 零售用户注册成功

**状态码：** `201 Created`

```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "token": "1|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "customer": {
      "id": 1,
      "first_name": "张",
      "last_name": "三",
      "name": "张 三",
      "gender": "male",
      "date_of_birth": "1990-05-15",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "status": 1,
      "group": {
        "id": 2,
        "code": "general",
        "name": "一般用户"
      },
      "profile": {
        "social_title": "Mr.",
        "zip_postal_code": "100000",
        "city": "北京",
        "state": "北京市",
        "country": "中国",
        "vat_number": null,
        "identification_number": null,
        "pec": null,
        "recipient_code": null,
        "address": null,
        "company": null,
        "invoice_type": null,
        "activity_types": [],
        "activity_types_string": "",
        "how_know_us": [],
        "how_know_us_string": "",
        "is_wholesale_profile": false
      },
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  }
}
```

#### 批发用户注册成功（等待审核）

**状态码：** `201 Created`

```json
{
  "success": false,
  "message": "账户等待管理员审核激活",
  "data": []
}
```

#### 批发用户注册并审核通过

**状态码：** `201 Created`

```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "token": "2|eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "customer": {
      "id": 2,
      "first_name": "李",
      "last_name": "四",
      "name": "李 四",
      "gender": "male",
      "date_of_birth": "1985-03-20",
      "email": "<EMAIL>",
      "phone": "13800138001",
      "status": 1,
      "group": {
        "id": 3,
        "code": "wholesale",
        "name": "批发用户"
      },
      "profile": {
        "social_title": "Mr.",
        "zip_postal_code": "100020",
        "city": "北京",
        "state": "北京市",
        "country": "中国",
        "vat_number": "*********",
        "identification_number": "110101198503201234",
        "pec": "<EMAIL>",
        "recipient_code": "ABC123",
        "address": "北京市朝阳区建国门外大街1号",
        "company": "李氏贸易有限公司",
        "invoice_type": "Invoice for company",
        "activity_types": [
          "Smartphone wholesaler",
          "Repair Center"
        ],
        "activity_types_string": "Smartphone wholesaler, Repair Center",
        "how_know_us": [
          "Web search",
          "Facebook"
        ],
        "how_know_us_string": "Web search, Facebook",
        "is_wholesale_profile": true
      },
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  }
}
```

### 错误响应

#### 验证失败

**状态码：** `422 Unprocessable Entity`

```json
{
  "success": false,
  "message": "验证失败",
  "errors": {
    "email": ["邮箱已经存在"],
    "phone": ["手机号已经存在"],
    "password": ["密码至少需要6位字符"]
  }
}
```

#### 服务器错误

**状态码：** `500 Internal Server Error`

```json
{
  "success": false,
  "message": "服务器内部错误"
}
```

---

## 特殊说明

### 用户类型说明

1. **零售用户 (`general`)**
   - 注册后立即激活
   - 可获得API Token进行后续操作
   - 主要收集基础个人信息

2. **批发用户 (`wholesale`)**
   - 注册后需要管理员审核
   - 审核通过前账户处于暂停状态 (`is_suspended = 1`)
   - 收集详细的商业信息
   - 审核通过后才能正常使用

3. **访客用户 (`guest`)**
   - 临时用户类型
   - 用于访客购买等场景

### 数据存储

- 基础用户信息存储在 `customers` 表
- 扩展信息存储在 `customer_profiles` 表
- 两表通过 `customer_id` 进行关联

### 安全性

- 密码会自动进行 bcrypt 加密
- 邮箱和手机号必须唯一
- 使用 Laravel Sanctum 生成 API Token

---

## 错误代码说明

| 错误代码 | 说明 |
|---------|------|
| 422 | 数据验证失败 |
| 401 | 认证失败 |
| 500 | 服务器内部错误 |

---

## 版本信息

- **版本：** v1.0
- **最后更新：** 2024-01-01
- **维护者：** MLK Web API Team 