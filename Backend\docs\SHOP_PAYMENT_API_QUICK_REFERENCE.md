# Shop包支付API快速参考

## 基础信息
- **基础URL**: `/api`
- **认证**: Session认证
- **中间件**: `web`, `shop`

## 核心支付API

### 结账流程API

| 接口 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/checkout/onepage/summary` | GET | 获取结账摘要和支付方式 | - |
| `/checkout/onepage/payment-methods` | POST | 保存支付方式 | `payment` |
| `/checkout/onepage/orders` | POST | 提交订单 | - |

### PayPal Standard API

| 接口 | 方法 | 描述 |
|------|------|------|
| `/paypal/standard/redirect` | GET | 重定向到PayPal |
| `/paypal/standard/success` | GET | 支付成功回调 |
| `/paypal/standard/cancel` | GET | 支付取消回调 |
| `/paypal/standard/ipn` | POST | PayPal IPN处理 |

### PayPal Smart Button API

| 接口 | 方法 | 描述 |
|------|------|------|
| `/paypal/smart-button/create-order` | GET | 创建PayPal订单 |
| `/paypal/smart-button/capture-order` | POST | 捕获PayPal支付 |

## 支付方式代码

| 支付方式 | 代码 | 重定向 |
|---------|------|--------|
| 货到付款 | `cashondelivery` | 否 |
| 银行转账 | `moneytransfer` | 否 |
| PayPal标准 | `paypal_standard` | 是 |
| PayPal智能按钮 | `paypal_smart_button` | 否 |

## 快速示例

### 保存支付方式
```bash
POST /api/checkout/onepage/payment-methods
{
    "payment": {
        "method": "cashondelivery"
    }
}
```

### 提交订单
```bash
POST /api/checkout/onepage/orders
# 响应：
{
    "data": {
        "redirect": true,
        "redirect_url": "https://paypal.com/..."
    }
}
```

### PayPal Smart Button流程
```bash
# 1. 创建订单
GET /paypal/smart-button/create-order

# 2. 捕获支付
POST /paypal/smart-button/capture-order
{
    "orderData": {
        "orderID": "PAYPAL_ORDER_ID"
    }
}
```

## 响应格式

### 成功响应
```json
{
    "data": {
        "cart": {...},
        "payment_methods": [...]
    }
}
```

### 重定向响应
```json
{
    "data": {
        "redirect": true,
        "redirect_url": "https://..."
    }
}
```

### 错误响应
```json
{
    "redirect_url": "/checkout/cart"
}
```

## 支付流程

### 标准流程
1. 获取支付方式 → 选择支付方式 → 提交订单 → 处理支付

### PayPal Standard流程
1. 选择PayPal → 提交订单 → 重定向PayPal → 支付完成 → 回调处理

### PayPal Smart Button流程
1. 创建PayPal订单 → 用户确认支付 → 捕获支付 → 创建系统订单

---

📖 **详细文档**: 查看 `SHOP_PACKAGE_PAYMENT_API_DOCS.md` 获取完整说明
