# 客户认证相关API接口文档

## 基础信息
- 基础URL: `/api/mlk/customer`
- 请求方式: POST
- 响应格式: JSON

## 接口列表

### 1. 忘记密码 - 发送重置密码邮件

**接口地址**: `POST /api/mlk/customer/forgot-password`

**请求参数**:
```json
{
    "email": "<EMAIL>"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱地址 |

**成功响应**:
```json
{
    "success": true,
    "message": "重置密码链接已发送到您的邮箱",
    "data": []
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "该邮箱地址不存在",
    "error_code": 404
}
```

---

### 2. 重置密码

**接口地址**: `POST /api/mlk/customer/reset-password`

**请求参数**:
```json
{
    "token": "password_reset_token",
    "email": "<EMAIL>",
    "password": "new_password",
    "password_confirmation": "new_password"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | string | 是 | 重置密码令牌（从邮件中获取） |
| email | string | 是 | 用户邮箱地址 |
| password | string | 是 | 新密码（最少6位） |
| password_confirmation | string | 是 | 确认新密码 |

**成功响应**:
```json
{
    "success": true,
    "message": "密码重置成功",
    "data": []
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "重置令牌无效或已过期",
    "error_code": 400
}
```

---

### 3. 验证邮箱

**接口地址**: `POST /api/mlk/customer/verify-account`

**请求参数**:
```json
{
    "token": "email_verification_token"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | string | 是 | 邮箱验证令牌（从验证邮件中获取） |

**成功响应**:
```json
{
    "success": true,
    "message": "邮箱验证成功",
    "data": []
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "验证令牌无效或已过期",
    "error_code": 404
}
```

---

### 4. 重新发送验证邮件

**接口地址**: `POST /api/mlk/customer/resend-verification`

**请求参数**:
```json
{
    "email": "<EMAIL>"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱地址 |

**成功响应**:
```json
{
    "success": true,
    "message": "验证邮件已重新发送",
    "data": []
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "该邮箱地址不存在",
    "error_code": 404
}
```

或

```json
{
    "success": false,
    "message": "该账户已经验证过了",
    "error_code": 400
}
```

---

### 5. 获取联系我们表单翻译

**接口地址**: `GET /api/mlk/customer/contact-us/translations`

**请求参数**: 无

**成功响应**:
```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        "title": "联系我们",
        "about": "给我们留言，我们会尽快回复您",
        "fields": {
            "name": {
                "label": "姓名",
                "placeholder": "姓名",
                "required": true
            },
            "email": {
                "label": "电子邮件",
                "placeholder": "电子邮件",
                "required": true
            },
            "contact": {
                "label": "电话号码",
                "placeholder": "电话号码",
                "required": false
            },
            "message": {
                "label": "您有什么问题？",
                "placeholder": "在这里描述",
                "required": true
            }
        },
        "submit_button": "提交",
        "success_message": "感谢您与我们联系，提供您的意见和问题。我们会尽快回复您。",
        "captcha_enabled": false
    }
}
```

**说明**:
- 返回当前系统语言的翻译内容
- `captcha_enabled` 表示验证码是否启用
- `fields` 中包含每个字段的标签、占位符和是否必填信息

---

### 6. 联系我们 - 发送留言

**接口地址**: `POST /api/mlk/customer/contact-us`

**请求参数**:
```json
{
    "name": "张三",
    "email": "<EMAIL>",
    "contact": "***********",
    "message": "我想了解更多关于产品的信息..."
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 联系人姓名 |
| email | string | 是 | 联系人邮箱地址 |
| contact | string | 否 | 联系人电话号码 |
| message | string | 是 | 留言内容 |

**成功响应**:
```json
{
    "success": true,
    "message": "感谢您的留言，我们会尽快回复",
    "data": []
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "参数验证失败",
    "error_code": 422
}
```

或（频率限制）

```json
{
    "success": false,
    "message": "请求过于频繁，请稍后再试",
    "error_code": 429
}
```

---

## 使用流程

### 重置密码流程
1. 用户调用 `/forgot-password` 接口，输入邮箱地址
2. 系统发送重置密码邮件到用户邮箱
3. 用户从邮件中获取重置令牌
4. 用户调用 `/reset-password` 接口，提供令牌和新密码
5. 密码重置成功

### 邮箱验证流程
1. 用户注册后，系统自动发送验证邮件
2. 用户从邮件中获取验证令牌
3. 用户调用 `/verify-account` 接口进行验证
4. 验证成功，账户激活

### 重新发送验证邮件流程
1. 如果用户没有收到验证邮件，可以调用 `/resend-verification` 接口
2. 系统重新发送验证邮件
3. 用户按照正常验证流程完成验证

### 联系我们流程
1. 前端调用 `/contact-us/translations` 接口获取表单字段翻译
2. 前端根据翻译内容渲染联系表单
3. 用户填写联系表单（姓名、邮箱、电话、留言内容）
4. 调用 `/contact-us` 接口提交留言
5. 系统发送邮件到管理员邮箱
6. 管理员可以直接回复用户邮箱

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误或业务逻辑错误 |
| 404 | 资源不存在（邮箱不存在、令牌无效等） |
| 422 | 表单验证失败 |
| 429 | 请求过于频繁（重置密码邮件发送限制） |
| 500 | 服务器内部错误 |

---

## 注意事项

1. **安全性**: 所有密码相关操作都需要验证令牌，确保安全性
2. **频率限制**: 
   - 忘记密码邮件发送有频率限制，避免恶意攻击
   - 联系我们接口限制每分钟最多5次请求，防止垃圾邮件
3. **令牌有效期**: 重置密码和邮箱验证令牌都有有效期限制
4. **邮件配置**: 确保系统邮件配置正确，以便正常发送邮件
5. **多语言支持**: 错误消息支持多语言，根据系统语言设置返回对应语言的消息
6. **联系我们功能**: 
   - 联系我们邮件会发送到管理员邮箱
   - 管理员可以直接回复用户邮箱进行沟通
   - 电话号码字段为可选，但建议提供以便更好的沟通

---

## 测试示例

### 使用cURL测试忘记密码接口

```bash
curl -X POST http://your-domain.com/api/mlk/customer/forgot-password \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

### 使用cURL测试重置密码接口

```bash
curl -X POST http://your-domain.com/api/mlk/customer/reset-password \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "token": "your_reset_token",
    "email": "<EMAIL>",
    "password": "newpassword123",
    "password_confirmation": "newpassword123"
  }'
```

### 使用cURL测试邮箱验证接口

```bash
curl -X POST http://your-domain.com/api/mlk/customer/verify-account \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "token": "your_verification_token"
  }'
```

### 使用cURL测试重新发送验证邮件接口

```bash
curl -X POST http://your-domain.com/api/mlk/customer/resend-verification \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

### 使用cURL测试获取翻译接口

```bash
curl -X GET http://your-domain.com/api/mlk/customer/contact-us/translations \
  -H "Accept: application/json"
```

### 使用cURL测试联系我们接口

```bash
curl -X POST http://your-domain.com/api/mlk/customer/contact-us \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "name": "张三",
    "email": "<EMAIL>",
    "contact": "***********",
    "message": "我想了解更多关于你们产品的信息，请联系我。"
  }'
``` 