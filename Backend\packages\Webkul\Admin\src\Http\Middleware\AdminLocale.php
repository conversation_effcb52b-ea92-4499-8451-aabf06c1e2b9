<?php

namespace Webkul\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AdminLocale
{
    /**
     * 支持的后台语言列表
     */
    protected $supportedLocales = [
        'en' => 'English',
        'zh_CN' => '中文',
        'it' => 'Italiano',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取语言设置的优先级：Cookie > Session > 默认
        $adminLocale = $this->getAdminLocale($request);
        
        // 只为当前请求设置语言，不改变系统默认语言
        app()->setLocale($adminLocale);
        
        // 将当前后台语言信息存储到视图中，供前端使用
        view()->share('currentAdminLocale', $adminLocale);
        view()->share('supportedAdminLocales', $this->supportedLocales);
        
        // 如果cookie中的语言与当前语言不同，更新cookie
        if ($request->cookie('admin_locale') !== $adminLocale) {
            $this->setAdminLocaleCookie($adminLocale);
        }
        
        // 同时保存到session，保持向后兼容性
        session(['admin_locale' => $adminLocale]);
        
        return $next($request);
    }
    
    /**
     * 获取管理员语言设置
     * 优先级：Cookie > Session > 默认语言
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function getAdminLocale(Request $request)
    {
        // 1. 优先从Cookie中获取
        $cookieLocale = $request->cookie('admin_locale');
        if ($cookieLocale && array_key_exists($cookieLocale, $this->supportedLocales)) {
            return $cookieLocale;
        }
        
        // 2. 从Session中获取
        $sessionLocale = session('admin_locale');
        if ($sessionLocale && array_key_exists($sessionLocale, $this->supportedLocales)) {
            return $sessionLocale;
        }
        
        // 3. 使用默认语言
        return 'en';
    }
    
    /**
     * 设置管理员语言Cookie
     *
     * @param  string  $locale
     * @return void
     */
    protected function setAdminLocaleCookie($locale)
    {
        // 动态获取后台路径前缀，确保路径格式正确
        $adminUrl = config('app.admin_url', 'admin');
        $adminPath = '/' . trim($adminUrl, '/');
        
        // 设置Cookie，有效期为1年
        cookie()->queue(cookie(
            'admin_locale',     // Cookie名称
            $locale,           // Cookie值
            60 * 24 * 365,     // 有效期：1年 (分钟)
            $adminPath,        // 路径：从配置中动态获取后台路径
            null,              // 域名（使用默认）
            false,             // secure：不要求HTTPS
            true,              // httpOnly：防止XSS攻击
            false,             // raw：不使用原始值
            'Lax'              // sameSite策略
        ));
    }
    
    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public function getSupportedLocales()
    {
        return $this->supportedLocales;
    }
}
