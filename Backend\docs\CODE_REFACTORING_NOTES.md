# 代码重构说明

## 重构目标

为了统一产品格式化逻辑并便于维护，将IndexController和CategoryProductController中的公共产品格式化代码抽离到独立的BaseProductFormatter类中。

## 重构内容

### 1. 新增文件

#### BaseProductFormatter.php
- **位置**: `packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php`
- **功能**: 包含所有产品格式化的公共方法
- **主要方法**:
  - `preloadProductPriceData()` - 预加载产品价格相关数据
  - `formatProducts()` - 格式化产品数据
  - `getProductColorValue()` - 获取产品颜色属性
  - `getProductDiscountInfo()` - 获取产品折扣信息
  - `getDiscountSources()` - 获取折扣来源信息
  - `formatDiscountData()` - 格式化折扣数据

### 2. 修改的文件

#### IndexController.php
- **变更**: 移除重复的产品格式化方法
- **新增**: 依赖注入BaseProductFormatter
- **修改**: 使用BaseProductFormatter的方法替代原有的格式化逻辑

#### CategoryProductController.php
- **变更**: 移除ProductResource的使用
- **新增**: 依赖注入BaseProductFormatter
- **修改**: 使用BaseProductFormatter格式化产品数据，确保与IndexController输出格式一致

## 产品数据格式

### 统一的产品输出格式
```json
{
    "id": 1,
    "name": "产品名称",
    "url_key": "product-url-key",
    "price": 85.00,
    "formatted_price": "$85.00",
    "base_price": 100.00,
    "formatted_base_price": "$100.00",
    "short_description": "产品简短描述",
    "description": "产品详细描述",
    "base_image": "http://example.com/image.jpg",
    "images": [...],
    "is_new": false,
    "is_featured": true,
    "color": {
        "option_id": 1,
        "option_value": "red",
        "option_label": "红色"
    },
    "discount": {
        "has_discount": true,
        "regular_price": 100.00,
        "final_price": 85.00,
        "discount_amount": 15.00,
        "discount_percentage": 15.00,
        "special_price": 85.00,
        "is_special_price_active": true,
        "discount_sources": [...]
    }
}
```

## 优势

1. **代码复用**: 消除了重复代码，提高了代码复用性
2. **统一格式**: 确保所有API接口返回一致的产品数据格式
3. **易于维护**: 产品格式化逻辑集中管理，修改时只需更新一处
4. **性能优化**: 统一的预加载策略，避免N+1查询问题
5. **可扩展性**: 新的控制器可以轻松使用相同的产品格式化逻辑

## 使用方法

### 在控制器中使用BaseProductFormatter

```php
class YourController extends APIController
{
    protected $productFormatter;

    public function __construct(CustomerRepository $customerRepository)
    {
        $this->productFormatter = new BaseProductFormatter($customerRepository);
    }

    public function yourMethod()
    {
        // 获取产品数据
        $products = $this->productRepository->getAll($params);
        
        // 预加载相关数据
        $this->productFormatter->preloadProductPriceData($products);
        
        // 格式化产品数据
        $formattedProducts = $this->productFormatter->formatProducts($products);
        
        return $this->success(['products' => $formattedProducts]);
    }
}
```

## 注意事项

1. **依赖注入**: 确保在控制器构造函数中正确注入CustomerRepository
2. **预加载**: 在格式化之前务必调用preloadProductPriceData()方法
3. **错误处理**: BaseProductFormatter中包含了完善的错误处理机制
4. **性能**: 该格式化器已针对大量产品数据进行了优化

## 兼容性

- 与现有的IndexController完全兼容
- CategoryProductController现在输出与IndexController相同格式的产品数据
- 所有现有的API客户端无需修改即可继续正常工作 