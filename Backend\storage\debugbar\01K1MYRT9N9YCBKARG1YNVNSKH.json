{"__meta": {"id": "01K1MYRT9N9YCBKARG1YNVNSKH", "datetime": "2025-08-02 09:35:57", "utime": **********.877888, "method": "GET", "uri": "/admin/catalog/products/edit/195?channel=default&locale=en", "ip": "127.0.0.1"}, "modules": {"count": 8, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\AttributeFamily (2)", "Webkul\\Attribute\\Models\\Attribute (57)", "Webkul\\Attribute\\Models\\AttributeGroup (7)", "Webkul\\Attribute\\Models\\AttributeOption (140)", "Webkul\\Attribute\\Models\\AttributeOptionTranslation (50)", "Webkul\\Attribute\\Models\\AttributeTranslation (18)"], "views": [], "queries": [{"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 2.19, "duration_str": "2.19s", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 2.29, "duration_str": "2.29s", "connection": "mlk"}, {"sql": "select * from `attribute_groups` where `attribute_groups`.`attribute_family_id` = 2 and `attribute_groups`.`attribute_family_id` is not null order by `position` asc", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 195", "duration": 2.33, "duration_str": "2.33s", "connection": "mlk"}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 8 and `code` not in ('color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 25 and `attribute_options`.`attribute_id` is not null and `parent_id` is null order by `sort_order` asc", "duration": 4.94, "duration_str": "4.94s", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 33 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "duration": 0.37, "duration_str": "370ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 34 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 9 and `code` not in ('color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 10 and `code` not in ('color', 'color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "duration": 0.61, "duration_str": "610ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `type` = 'multiimage'", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 195", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (23)", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` in (23)", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 495, 496, 497, 498)", "duration": 1.99, "duration_str": "1.99s", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 23 and `attribute_translations`.`attribute_id` is not null", "duration": 1.8, "duration_str": "1.8s", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 23 and `attribute_translations`.`attribute_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = 23 and `attribute_translations`.`attribute_id` is not null", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 11 and `code` not in ('color', 'color', 'color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "duration": 1.88, "duration_str": "1.88s", "connection": "mlk"}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 12 and `code` not in ('color', 'color', 'color', 'color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 13 and `code` not in ('color', 'color', 'color', 'color', 'color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 14 and `code` not in ('color', 'color', 'color', 'color', 'color', 'color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "duration": 0.39, "duration_str": "390ms", "connection": "mlk"}]}, {"name": "Webkul\\Category", "models": ["Webkul\\Category\\Models\\Category (1)", "Webkul\\Category\\Models\\CategoryTranslation (5)"], "views": [], "queries": [{"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` = 195", "duration": 3.89, "duration_str": "3.89s", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (13)", "duration": 1.68, "duration_str": "1.68s", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (586)", "Webkul\\Core\\Models\\ChannelTranslation (10)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.4, "duration_str": "400ms", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.4, "duration_str": "400ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.56, "duration_str": "560ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.39, "duration_str": "390ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.5, "duration_str": "500ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `currencies` limit 1", "duration": 1.91, "duration_str": "1.91s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.33, "duration_str": "330ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.38, "duration_str": "380ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.39, "duration_str": "390ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.37, "duration_str": "370ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.33, "duration_str": "330ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (3)"], "views": [], "queries": [{"sql": "select * from `customer_groups`", "duration": 1.91, "duration_str": "1.91s", "connection": "mlk"}]}, {"name": "Webkul\\Inventory", "models": ["Webkul\\Inventory\\Models\\InventorySource (1)"], "views": [], "queries": [{"sql": "select * from `inventory_sources` where `status` = 1", "duration": 1.97, "duration_str": "1.97s", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (3)", "Webkul\\Product\\Models\\ProductAttributeValue (57)", "Webkul\\Product\\Models\\ProductImage (3)", "Webkul\\Product\\Models\\ProductInventory (2)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = 195 limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 195 and `product_attribute_values`.`product_id` is not null", "duration": 2.32, "duration_str": "2.32s", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 195 and `product_images`.`product_id` is not null order by `position` asc", "duration": 1.95, "duration_str": "1.95s", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` = 195 and `product_videos`.`product_id` is not null order by `position` asc", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (196, 197) order by `position` asc", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` in (196, 197)", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 196 and `product_attribute_values`.`product_id` is not null", "duration": 1.82, "duration_str": "1.82s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 197 and `product_attribute_values`.`product_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `products`.*, `product_up_sells`.`parent_id` as `pivot_parent_id`, `product_up_sells`.`child_id` as `pivot_child_id` from `products` inner join `product_up_sells` on `products`.`id` = `product_up_sells`.`child_id` where `product_up_sells`.`parent_id` = 195", "duration": 1.93, "duration_str": "1.93s", "connection": "mlk"}, {"sql": "select `products`.*, `product_cross_sells`.`parent_id` as `pivot_parent_id`, `product_cross_sells`.`child_id` as `pivot_child_id` from `products` inner join `product_cross_sells` on `products`.`id` = `product_cross_sells`.`child_id` where `product_cross_sells`.`parent_id` = 195", "duration": 1.7, "duration_str": "1.7s", "connection": "mlk"}, {"sql": "select `products`.*, `product_relations`.`parent_id` as `pivot_parent_id`, `product_relations`.`child_id` as `pivot_child_id` from `products` inner join `product_relations` on `products`.`id` = `product_relations`.`child_id` where `product_relations`.`parent_id` = 195", "duration": 1.68, "duration_str": "1.68s", "connection": "mlk"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 195 and `product_customer_group_prices`.`product_id` is not null", "duration": 1.77, "duration_str": "1.77s", "connection": "mlk"}]}, {"name": "Webkul\\Tax", "models": ["Webkul\\Tax\\Models\\TaxCategory (2)"], "views": [], "queries": [{"sql": "select * from `tax_categories`", "duration": 1.66, "duration_str": "1.66s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 2.14, "duration_str": "2.14s", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754123756.973978, "end": **********.894149, "duration": 0.9201710224151611, "duration_str": "920ms", "measures": [{"label": "Booting", "start": 1754123756.973978, "relative_start": 0, "end": **********.2332, "relative_end": **********.2332, "duration": 0.*****************, "duration_str": "259ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.233212, "relative_start": 0.****************, "end": **********.894151, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "661ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.247413, "relative_start": 0.****************, "end": **********.250391, "relative_end": **********.250391, "duration": 0.002978086471557617, "duration_str": "2.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.30122, "relative_start": 0.****************, "end": **********.876259, "relative_end": **********.876259, "duration": 0.****************, "duration_str": "575ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: admin::catalog.products.edit", "start": **********.303033, "relative_start": 0.****************, "end": **********.303033, "relative_end": **********.303033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.364953, "relative_start": 0.****************, "end": **********.364953, "relative_end": **********.364953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.367409, "relative_start": 0.3934309482574463, "end": **********.367409, "relative_end": **********.367409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.379207, "relative_start": 0.405228853225708, "end": **********.379207, "relative_end": **********.379207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.379734, "relative_start": 0.40575599670410156, "end": **********.379734, "relative_end": **********.379734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.387884, "relative_start": 0.4139058589935303, "end": **********.387884, "relative_end": **********.387884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.388417, "relative_start": 0.41443896293640137, "end": **********.388417, "relative_end": **********.388417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.388988, "relative_start": 0.4150099754333496, "end": **********.388988, "relative_end": **********.388988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.389327, "relative_start": 0.41534900665283203, "end": **********.389327, "relative_end": **********.389327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.396504, "relative_start": 0.42252588272094727, "end": **********.396504, "relative_end": **********.396504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.396852, "relative_start": 0.42287397384643555, "end": **********.396852, "relative_end": **********.396852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.397226, "relative_start": 0.4232480525970459, "end": **********.397226, "relative_end": **********.397226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.397509, "relative_start": 0.42353105545043945, "end": **********.397509, "relative_end": **********.397509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.401291, "relative_start": 0.42731285095214844, "end": **********.401291, "relative_end": **********.401291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.401649, "relative_start": 0.427670955657959, "end": **********.401649, "relative_end": **********.401649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.402047, "relative_start": 0.4280688762664795, "end": **********.402047, "relative_end": **********.402047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.402337, "relative_start": 0.4283590316772461, "end": **********.402337, "relative_end": **********.402337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.40586, "relative_start": 0.4318819046020508, "end": **********.40586, "relative_end": **********.40586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.406182, "relative_start": 0.432204008102417, "end": **********.406182, "relative_end": **********.406182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.406554, "relative_start": 0.43257594108581543, "end": **********.406554, "relative_end": **********.406554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.406834, "relative_start": 0.43285584449768066, "end": **********.406834, "relative_end": **********.406834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.40767, "relative_start": 0.43369197845458984, "end": **********.40767, "relative_end": **********.40767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.407976, "relative_start": 0.43399786949157715, "end": **********.407976, "relative_end": **********.407976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.408333, "relative_start": 0.4343550205230713, "end": **********.408333, "relative_end": **********.408333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.408608, "relative_start": 0.4346299171447754, "end": **********.408608, "relative_end": **********.408608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.cascading-select-control", "start": **********.409333, "relative_start": 0.43535494804382324, "end": **********.409333, "relative_end": **********.409333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.419041, "relative_start": 0.44506287574768066, "end": **********.419041, "relative_end": **********.419041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.41995, "relative_start": 0.4459719657897949, "end": **********.41995, "relative_end": **********.41995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.420296, "relative_start": 0.4463179111480713, "end": **********.420296, "relative_end": **********.420296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.420696, "relative_start": 0.4467179775238037, "end": **********.420696, "relative_end": **********.420696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.420986, "relative_start": 0.4470078945159912, "end": **********.420986, "relative_end": **********.420986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.428315, "relative_start": 0.45433688163757324, "end": **********.428315, "relative_end": **********.428315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.428968, "relative_start": 0.4549899101257324, "end": **********.428968, "relative_end": **********.428968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.429287, "relative_start": 0.4553089141845703, "end": **********.429287, "relative_end": **********.429287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.429681, "relative_start": 0.4557030200958252, "end": **********.429681, "relative_end": **********.429681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.429969, "relative_start": 0.4559910297393799, "end": **********.429969, "relative_end": **********.429969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.433669, "relative_start": 0.45969104766845703, "end": **********.433669, "relative_end": **********.433669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.434019, "relative_start": 0.4600410461425781, "end": **********.434019, "relative_end": **********.434019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.434412, "relative_start": 0.4604339599609375, "end": **********.434412, "relative_end": **********.434412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.434718, "relative_start": 0.4607398509979248, "end": **********.434718, "relative_end": **********.434718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.439893, "relative_start": 0.4659149646759033, "end": **********.439893, "relative_end": **********.439893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.440565, "relative_start": 0.4665870666503906, "end": **********.440565, "relative_end": **********.440565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.440894, "relative_start": 0.4669158458709717, "end": **********.440894, "relative_end": **********.440894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.442847, "relative_start": 0.4688689708709717, "end": **********.442847, "relative_end": **********.442847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.443144, "relative_start": 0.4691660404205322, "end": **********.443144, "relative_end": **********.443144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.451064, "relative_start": 0.47708606719970703, "end": **********.451064, "relative_end": **********.451064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.45194, "relative_start": 0.47796201705932617, "end": **********.45194, "relative_end": **********.45194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.452344, "relative_start": 0.4783658981323242, "end": **********.452344, "relative_end": **********.452344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.452889, "relative_start": 0.4789109230041504, "end": **********.452889, "relative_end": **********.452889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.453295, "relative_start": 0.47931694984436035, "end": **********.453295, "relative_end": **********.453295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.461768, "relative_start": 0.4877898693084717, "end": **********.461768, "relative_end": **********.461768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.tinymce.index", "start": **********.462598, "relative_start": 0.4886200428009033, "end": **********.462598, "relative_end": **********.462598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.463549, "relative_start": 0.48957085609436035, "end": **********.463549, "relative_end": **********.463549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.463983, "relative_start": 0.4900050163269043, "end": **********.463983, "relative_end": **********.463983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.464592, "relative_start": 0.4906139373779297, "end": **********.464592, "relative_end": **********.464592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.464943, "relative_start": 0.4909648895263672, "end": **********.464943, "relative_end": **********.464943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.465266, "relative_start": 0.4912879467010498, "end": **********.465266, "relative_end": **********.465266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.465578, "relative_start": 0.49160003662109375, "end": **********.465578, "relative_end": **********.465578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.46618, "relative_start": 0.4922020435333252, "end": **********.46618, "relative_end": **********.46618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.46653, "relative_start": 0.4925520420074463, "end": **********.46653, "relative_end": **********.46653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.468164, "relative_start": 0.4941859245300293, "end": **********.468164, "relative_end": **********.468164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.468481, "relative_start": 0.4945030212402344, "end": **********.468481, "relative_end": **********.468481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.469093, "relative_start": 0.4951150417327881, "end": **********.469093, "relative_end": **********.469093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.button.index", "start": **********.469523, "relative_start": 0.4955449104309082, "end": **********.469523, "relative_end": **********.469523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.index", "start": **********.469973, "relative_start": 0.49599504470825195, "end": **********.469973, "relative_end": **********.469973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.470486, "relative_start": 0.4965078830718994, "end": **********.470486, "relative_end": **********.470486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.474139, "relative_start": 0.5001609325408936, "end": **********.474139, "relative_end": **********.474139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.474483, "relative_start": 0.5005049705505371, "end": **********.474483, "relative_end": **********.474483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.474886, "relative_start": 0.5009078979492188, "end": **********.474886, "relative_end": **********.474886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.475183, "relative_start": 0.5012049674987793, "end": **********.475183, "relative_end": **********.475183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.475937, "relative_start": 0.5019588470458984, "end": **********.475937, "relative_end": **********.475937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.476253, "relative_start": 0.5022749900817871, "end": **********.476253, "relative_end": **********.476253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.seo.index", "start": **********.478685, "relative_start": 0.504706859588623, "end": **********.478685, "relative_end": **********.478685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.47931, "relative_start": 0.5053319931030273, "end": **********.47931, "relative_end": **********.47931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.479634, "relative_start": 0.5056560039520264, "end": **********.479634, "relative_end": **********.479634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.486949, "relative_start": 0.5129709243774414, "end": **********.486949, "relative_end": **********.486949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.487704, "relative_start": 0.513725996017456, "end": **********.487704, "relative_end": **********.487704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.48805, "relative_start": 0.5140719413757324, "end": **********.48805, "relative_end": **********.48805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.488468, "relative_start": 0.5144898891448975, "end": **********.488468, "relative_end": **********.488468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.488766, "relative_start": 0.5147879123687744, "end": **********.488766, "relative_end": **********.488766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.494562, "relative_start": 0.5205838680267334, "end": **********.494562, "relative_end": **********.494562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.49522, "relative_start": 0.5212419033050537, "end": **********.49522, "relative_end": **********.49522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.495549, "relative_start": 0.5215709209442139, "end": **********.495549, "relative_end": **********.495549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.495932, "relative_start": 0.5219540596008301, "end": **********.495932, "relative_end": **********.495932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.49622, "relative_start": 0.5222420692443848, "end": **********.49622, "relative_end": **********.49622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.502389, "relative_start": 0.5284109115600586, "end": **********.502389, "relative_end": **********.502389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.503128, "relative_start": 0.5291500091552734, "end": **********.503128, "relative_end": **********.503128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.503477, "relative_start": 0.5294990539550781, "end": **********.503477, "relative_end": **********.503477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.images", "start": **********.503894, "relative_start": 0.5299160480499268, "end": **********.503894, "relative_end": **********.503894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.media.images", "start": **********.507336, "relative_start": 0.5333578586578369, "end": **********.507336, "relative_end": **********.507336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.image.index", "start": **********.514977, "relative_start": 0.5409989356994629, "end": **********.514977, "relative_end": **********.514977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.515504, "relative_start": 0.5415258407592773, "end": **********.515504, "relative_end": **********.515504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.515858, "relative_start": 0.5418798923492432, "end": **********.515858, "relative_end": **********.515858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.516493, "relative_start": 0.5425150394439697, "end": **********.516493, "relative_end": **********.516493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.516819, "relative_start": 0.5428409576416016, "end": **********.516819, "relative_end": **********.516819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.517132, "relative_start": 0.5431540012359619, "end": **********.517132, "relative_end": **********.517132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.517469, "relative_start": 0.5434908866882324, "end": **********.517469, "relative_end": **********.517469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.518059, "relative_start": 0.5440809726715088, "end": **********.518059, "relative_end": **********.518059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.518364, "relative_start": 0.5443859100341797, "end": **********.518364, "relative_end": **********.518364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.518668, "relative_start": 0.5446898937225342, "end": **********.518668, "relative_end": **********.518668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.518981, "relative_start": 0.5450029373168945, "end": **********.518981, "relative_end": **********.518981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.519534, "relative_start": 0.5455560684204102, "end": **********.519534, "relative_end": **********.519534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.519832, "relative_start": 0.545853853225708, "end": **********.519832, "relative_end": **********.519832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.520127, "relative_start": 0.5461490154266357, "end": **********.520127, "relative_end": **********.520127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.520439, "relative_start": 0.5464608669281006, "end": **********.520439, "relative_end": **********.520439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.520997, "relative_start": 0.5470190048217773, "end": **********.520997, "relative_end": **********.520997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.521298, "relative_start": 0.5473198890686035, "end": **********.521298, "relative_end": **********.521298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.52159, "relative_start": 0.5476119518280029, "end": **********.52159, "relative_end": **********.52159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.521905, "relative_start": 0.5479269027709961, "end": **********.521905, "relative_end": **********.521905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.522455, "relative_start": 0.5484769344329834, "end": **********.522455, "relative_end": **********.522455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.522755, "relative_start": 0.5487768650054932, "end": **********.522755, "relative_end": **********.522755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.button.index", "start": **********.523192, "relative_start": 0.5492138862609863, "end": **********.523192, "relative_end": **********.523192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.index", "start": **********.52349, "relative_start": 0.5495119094848633, "end": **********.52349, "relative_end": **********.52349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.523848, "relative_start": 0.5498700141906738, "end": **********.523848, "relative_end": **********.523848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.528332, "relative_start": 0.5543539524078369, "end": **********.528332, "relative_end": **********.528332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.multiimages", "start": **********.528746, "relative_start": 0.5547678470611572, "end": **********.528746, "relative_end": **********.528746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.image.index", "start": **********.53368, "relative_start": 0.5597019195556641, "end": **********.53368, "relative_end": **********.53368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.534043, "relative_start": 0.5600650310516357, "end": **********.534043, "relative_end": **********.534043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.videos", "start": **********.534505, "relative_start": 0.5605268478393555, "end": **********.534505, "relative_end": **********.534505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.media.videos", "start": **********.538016, "relative_start": 0.5640380382537842, "end": **********.538016, "relative_end": **********.538016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.538508, "relative_start": 0.5645298957824707, "end": **********.538508, "relative_end": **********.538508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.types.configurable", "start": **********.538968, "relative_start": 0.5649900436401367, "end": **********.538968, "relative_end": **********.538968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.544015, "relative_start": 0.5700368881225586, "end": **********.544015, "relative_end": **********.544015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.544312, "relative_start": 0.5703339576721191, "end": **********.544312, "relative_end": **********.544312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.button.index", "start": **********.544638, "relative_start": 0.570659875869751, "end": **********.544638, "relative_end": **********.544638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.index", "start": **********.544916, "relative_start": 0.5709378719329834, "end": **********.544916, "relative_end": **********.544916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.545269, "relative_start": 0.5712909698486328, "end": **********.545269, "relative_end": **********.545269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.545611, "relative_start": 0.5716328620910645, "end": **********.545611, "relative_end": **********.545611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.menu.item", "start": **********.546072, "relative_start": 0.5720939636230469, "end": **********.546072, "relative_end": **********.546072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.546437, "relative_start": 0.5724589824676514, "end": **********.546437, "relative_end": **********.546437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.546957, "relative_start": 0.5729789733886719, "end": **********.546957, "relative_end": **********.546957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.551592, "relative_start": 0.5776140689849854, "end": **********.551592, "relative_end": **********.551592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.552295, "relative_start": 0.5783169269561768, "end": **********.552295, "relative_end": **********.552295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.552656, "relative_start": 0.5786778926849365, "end": **********.552656, "relative_end": **********.552656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.553012, "relative_start": 0.5790338516235352, "end": **********.553012, "relative_end": **********.553012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.553292, "relative_start": 0.5793139934539795, "end": **********.553292, "relative_end": **********.553292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.553608, "relative_start": 0.5796298980712891, "end": **********.553608, "relative_end": **********.553608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.553908, "relative_start": 0.5799300670623779, "end": **********.553908, "relative_end": **********.553908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.554485, "relative_start": 0.5805070400238037, "end": **********.554485, "relative_end": **********.554485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.554776, "relative_start": 0.5807979106903076, "end": **********.554776, "relative_end": **********.554776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.555102, "relative_start": 0.5811240673065186, "end": **********.555102, "relative_end": **********.555102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.555397, "relative_start": 0.5814189910888672, "end": **********.555397, "relative_end": **********.555397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.555962, "relative_start": 0.5819840431213379, "end": **********.555962, "relative_end": **********.555962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.556249, "relative_start": 0.5822708606719971, "end": **********.556249, "relative_end": **********.556249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.556567, "relative_start": 0.5825889110565186, "end": **********.556567, "relative_end": **********.556567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.556872, "relative_start": 0.5828938484191895, "end": **********.556872, "relative_end": **********.556872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.557414, "relative_start": 0.5834360122680664, "end": **********.557414, "relative_end": **********.557414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.557707, "relative_start": 0.5837290287017822, "end": **********.557707, "relative_end": **********.557707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.558008, "relative_start": 0.5840299129486084, "end": **********.558008, "relative_end": **********.558008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.558406, "relative_start": 0.584428071975708, "end": **********.558406, "relative_end": **********.558406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.55859, "relative_start": 0.5846118927001953, "end": **********.55859, "relative_end": **********.55859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.558757, "relative_start": 0.5847790241241455, "end": **********.558757, "relative_end": **********.558757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.558917, "relative_start": 0.5849390029907227, "end": **********.558917, "relative_end": **********.558917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.559178, "relative_start": 0.5852000713348389, "end": **********.559178, "relative_end": **********.559178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.559439, "relative_start": 0.585460901260376, "end": **********.559439, "relative_end": **********.559439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.559618, "relative_start": 0.5856399536132812, "end": **********.559618, "relative_end": **********.559618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.559777, "relative_start": 0.585798978805542, "end": **********.559777, "relative_end": **********.559777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.drawer.index", "start": **********.560126, "relative_start": 0.5861480236053467, "end": **********.560126, "relative_end": **********.560126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.5606, "relative_start": 0.5866219997406006, "end": **********.5606, "relative_end": **********.5606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.561041, "relative_start": 0.5870630741119385, "end": **********.561041, "relative_end": **********.561041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.561629, "relative_start": 0.5876510143280029, "end": **********.561629, "relative_end": **********.561629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.561933, "relative_start": 0.5879549980163574, "end": **********.561933, "relative_end": **********.561933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.562483, "relative_start": 0.5885050296783447, "end": **********.562483, "relative_end": **********.562483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.562808, "relative_start": 0.5888299942016602, "end": **********.562808, "relative_end": **********.562808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.563105, "relative_start": 0.5891270637512207, "end": **********.563105, "relative_end": **********.563105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.5634, "relative_start": 0.5894219875335693, "end": **********.5634, "relative_end": **********.5634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.563967, "relative_start": 0.5899889469146729, "end": **********.563967, "relative_end": **********.563967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.564281, "relative_start": 0.5903029441833496, "end": **********.564281, "relative_end": **********.564281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.564576, "relative_start": 0.5905978679656982, "end": **********.564576, "relative_end": **********.564576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.564905, "relative_start": 0.5909268856048584, "end": **********.564905, "relative_end": **********.564905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.56551, "relative_start": 0.5915319919586182, "end": **********.56551, "relative_end": **********.56551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.56582, "relative_start": 0.5918419361114502, "end": **********.56582, "relative_end": **********.56582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.566123, "relative_start": 0.5921449661254883, "end": **********.566123, "relative_end": **********.566123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.566436, "relative_start": 0.5924580097198486, "end": **********.566436, "relative_end": **********.566436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.567018, "relative_start": 0.5930399894714355, "end": **********.567018, "relative_end": **********.567018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.567334, "relative_start": 0.5933558940887451, "end": **********.567334, "relative_end": **********.567334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.567634, "relative_start": 0.593656063079834, "end": **********.567634, "relative_end": **********.567634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.567931, "relative_start": 0.5939528942108154, "end": **********.567931, "relative_end": **********.567931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.568541, "relative_start": 0.5945630073547363, "end": **********.568541, "relative_end": **********.568541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.568848, "relative_start": 0.59486985206604, "end": **********.568848, "relative_end": **********.568848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.569136, "relative_start": 0.5951578617095947, "end": **********.569136, "relative_end": **********.569136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.569398, "relative_start": 0.5954198837280273, "end": **********.569398, "relative_end": **********.569398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.drawer.index", "start": **********.569756, "relative_start": 0.5957779884338379, "end": **********.569756, "relative_end": **********.569756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.570132, "relative_start": 0.596153974533081, "end": **********.570132, "relative_end": **********.570132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.links", "start": **********.727257, "relative_start": 0.7532789707183838, "end": **********.727257, "relative_end": **********.727257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.products.search", "start": **********.728094, "relative_start": 0.7541160583496094, "end": **********.728094, "relative_end": **********.728094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.drawer.index", "start": **********.728727, "relative_start": 0.754749059677124, "end": **********.728727, "relative_end": **********.728727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.791938, "relative_start": 0.8179600238800049, "end": **********.791938, "relative_end": **********.791938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.792293, "relative_start": 0.8183150291442871, "end": **********.792293, "relative_end": **********.792293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.797381, "relative_start": 0.8234028816223145, "end": **********.797381, "relative_end": **********.797381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.798054, "relative_start": 0.8240759372711182, "end": **********.798054, "relative_end": **********.798054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.798443, "relative_start": 0.8244650363922119, "end": **********.798443, "relative_end": **********.798443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.price.group", "start": **********.7989, "relative_start": 0.8249218463897705, "end": **********.7989, "relative_end": **********.7989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.catalog.products.edit.group-price", "start": **********.799878, "relative_start": 0.8258998394012451, "end": **********.799878, "relative_end": **********.799878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.800487, "relative_start": 0.8265089988708496, "end": **********.800487, "relative_end": **********.800487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.800819, "relative_start": 0.826840877532959, "end": **********.800819, "relative_end": **********.800819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.801404, "relative_start": 0.8274259567260742, "end": **********.801404, "relative_end": **********.801404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.801705, "relative_start": 0.8277268409729004, "end": **********.801705, "relative_end": **********.801705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.802001, "relative_start": 0.8280229568481445, "end": **********.802001, "relative_end": **********.802001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.802568, "relative_start": 0.828589916229248, "end": **********.802568, "relative_end": **********.802568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.802873, "relative_start": 0.828894853591919, "end": **********.802873, "relative_end": **********.802873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.803163, "relative_start": 0.8291850090026855, "end": **********.803163, "relative_end": **********.803163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.803469, "relative_start": 0.8294909000396729, "end": **********.803469, "relative_end": **********.803469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.804025, "relative_start": 0.8300468921661377, "end": **********.804025, "relative_end": **********.804025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.804323, "relative_start": 0.8303449153900146, "end": **********.804323, "relative_end": **********.804323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.804609, "relative_start": 0.8306310176849365, "end": **********.804609, "relative_end": **********.804609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.804898, "relative_start": 0.8309199810028076, "end": **********.804898, "relative_end": **********.804898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.805444, "relative_start": 0.8314659595489502, "end": **********.805444, "relative_end": **********.805444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.805749, "relative_start": 0.8317708969116211, "end": **********.805749, "relative_end": **********.805749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.button.index", "start": **********.806071, "relative_start": 0.8320930004119873, "end": **********.806071, "relative_end": **********.806071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.button.index", "start": **********.806269, "relative_start": 0.8322908878326416, "end": **********.806269, "relative_end": **********.806269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.index", "start": **********.806518, "relative_start": 0.8325400352478027, "end": **********.806518, "relative_end": **********.806518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.806865, "relative_start": 0.8328869342803955, "end": **********.806865, "relative_end": **********.806865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.813163, "relative_start": 0.8391849994659424, "end": **********.813163, "relative_end": **********.813163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.813473, "relative_start": 0.8394949436187744, "end": **********.813473, "relative_end": **********.813473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.817402, "relative_start": 0.8434238433837891, "end": **********.817402, "relative_end": **********.817402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.818186, "relative_start": 0.8442080020904541, "end": **********.818186, "relative_end": **********.818186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.818562, "relative_start": 0.8445839881896973, "end": **********.818562, "relative_end": **********.818562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.818999, "relative_start": 0.8450210094451904, "end": **********.818999, "relative_end": **********.818999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.819332, "relative_start": 0.8453538417816162, "end": **********.819332, "relative_end": **********.819332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.822457, "relative_start": 0.8484790325164795, "end": **********.822457, "relative_end": **********.822457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.823103, "relative_start": 0.8491249084472656, "end": **********.823103, "relative_end": **********.823103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.82343, "relative_start": 0.849452018737793, "end": **********.82343, "relative_end": **********.82343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.823815, "relative_start": 0.849837064743042, "end": **********.823815, "relative_end": **********.823815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.824136, "relative_start": 0.8501579761505127, "end": **********.824136, "relative_end": **********.824136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.824935, "relative_start": 0.850956916809082, "end": **********.824935, "relative_end": **********.824935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.825595, "relative_start": 0.8516168594360352, "end": **********.825595, "relative_end": **********.825595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.825906, "relative_start": 0.8519279956817627, "end": **********.825906, "relative_end": **********.825906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.826276, "relative_start": 0.8522980213165283, "end": **********.826276, "relative_end": **********.826276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.826567, "relative_start": 0.8525888919830322, "end": **********.826567, "relative_end": **********.826567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.827317, "relative_start": 0.8533389568328857, "end": **********.827317, "relative_end": **********.827317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.827956, "relative_start": 0.8539779186248779, "end": **********.827956, "relative_end": **********.827956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.828263, "relative_start": 0.8542850017547607, "end": **********.828263, "relative_end": **********.828263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.label", "start": **********.828627, "relative_start": 0.8546490669250488, "end": **********.828627, "relative_end": **********.828627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.controls", "start": **********.828907, "relative_start": 0.8549289703369141, "end": **********.828907, "relative_end": **********.828907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.control", "start": **********.831943, "relative_start": 0.8579649925231934, "end": **********.831943, "relative_end": **********.831943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.error", "start": **********.832603, "relative_start": 0.8586249351501465, "end": **********.832603, "relative_end": **********.832603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.control-group.index", "start": **********.832926, "relative_start": 0.8589479923248291, "end": **********.832926, "relative_end": **********.832926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.channels", "start": **********.834141, "relative_start": 0.8601629734039307, "end": **********.834141, "relative_end": **********.834141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::catalog.products.edit.categories", "start": **********.834746, "relative_start": 0.8607678413391113, "end": **********.834746, "relative_end": **********.834746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.tree.index", "start": **********.835411, "relative_start": 0.8614330291748047, "end": **********.835411, "relative_end": **********.835411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.tree.index", "start": **********.835703, "relative_start": 0.861724853515625, "end": **********.835703, "relative_end": **********.835703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.tree.view", "start": **********.843437, "relative_start": 0.8694589138031006, "end": **********.843437, "relative_end": **********.843437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.tree.checkbox", "start": **********.843994, "relative_start": 0.8700158596038818, "end": **********.843994, "relative_end": **********.843994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.tree.index", "start": **********.844377, "relative_start": 0.870398998260498, "end": **********.844377, "relative_end": **********.844377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.844814, "relative_start": 0.8708360195159912, "end": **********.844814, "relative_end": **********.844814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.index", "start": **********.845497, "relative_start": 0.8715188503265381, "end": **********.845497, "relative_end": **********.845497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flash-group.index", "start": **********.849304, "relative_start": 0.8753259181976318, "end": **********.849304, "relative_end": **********.849304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.flash-group.item", "start": **********.849927, "relative_start": 0.8759489059448242, "end": **********.849927, "relative_end": **********.849927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.modal.confirm", "start": **********.85049, "relative_start": 0.8765120506286621, "end": **********.85049, "relative_end": **********.85049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.header.index", "start": **********.851137, "relative_start": 0.8771588802337646, "end": **********.851137, "relative_end": **********.851137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.853363, "relative_start": 0.8793849945068359, "end": **********.853363, "relative_end": **********.853363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.form.index", "start": **********.853843, "relative_start": 0.8798649311065674, "end": **********.853843, "relative_end": **********.853843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.854217, "relative_start": 0.8802390098571777, "end": **********.854217, "relative_end": **********.854217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.drawer.index", "start": **********.863871, "relative_start": 0.8898930549621582, "end": **********.863871, "relative_end": **********.863871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.products", "start": **********.864406, "relative_start": 0.8904280662536621, "end": **********.864406, "relative_end": **********.864406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.orders", "start": **********.864991, "relative_start": 0.8910129070281982, "end": **********.864991, "relative_end": **********.864991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.categories", "start": **********.865505, "relative_start": 0.8915269374847412, "end": **********.865505, "relative_end": **********.865505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.shimmer.header.mega-search.customers", "start": **********.866018, "relative_start": 0.8920400142669678, "end": **********.866018, "relative_end": **********.866018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.dropdown.index", "start": **********.866611, "relative_start": 0.8926329612731934, "end": **********.866611, "relative_end": **********.866611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.sidebar.index", "start": **********.867268, "relative_start": 0.8932900428771973, "end": **********.867268, "relative_end": **********.867268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: admin::components.layouts.tabs", "start": **********.87483, "relative_start": 0.9008519649505615, "end": **********.87483, "relative_end": **********.87483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 50046624, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 262, "nb_templates": 262, "templates": [{"name": "1x admin::catalog.products.edit", "param_count": null, "params": [], "start": **********.302999, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit.blade.phpadmin::catalog.products.edit", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit"}, {"name": "7x admin::components.dropdown.index", "param_count": null, "params": [], "start": **********.36491, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/index.blade.phpadmin::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 7, "name_original": "admin::components.dropdown.index"}, {"name": "46x admin::components.form.control-group.label", "param_count": null, "params": [], "start": **********.379183, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/label.blade.phpadmin::components.form.control-group.label", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 46, "name_original": "admin::components.form.control-group.label"}, {"name": "21x admin::catalog.products.edit.controls", "param_count": null, "params": [], "start": **********.37971, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.phpadmin::catalog.products.edit.controls", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fcontrols.blade.php&line=1", "ajax": false, "filename": "controls.blade.php", "line": "?"}, "render_count": 21, "name_original": "admin::catalog.products.edit.controls"}, {"name": "43x admin::components.form.control-group.error", "param_count": null, "params": [], "start": **********.387851, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/error.blade.phpadmin::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 43, "name_original": "admin::components.form.control-group.error"}, {"name": "52x admin::components.form.control-group.index", "param_count": null, "params": [], "start": **********.388387, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/index.blade.phpadmin::components.form.control-group.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 52, "name_original": "admin::components.form.control-group.index"}, {"name": "1x admin::catalog.products.edit.cascading-select-control", "param_count": null, "params": [], "start": **********.409312, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/cascading-select-control.blade.phpadmin::catalog.products.edit.cascading-select-control", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fcascading-select-control.blade.php&line=1", "ajax": false, "filename": "cascading-select-control.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.cascading-select-control"}, {"name": "36x admin::components.form.control-group.control", "param_count": null, "params": [], "start": **********.419017, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/form/control-group/control.blade.phpadmin::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 36, "name_original": "admin::components.form.control-group.control"}, {"name": "1x admin::components.tinymce.index", "param_count": null, "params": [], "start": **********.462575, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/tinymce/index.blade.phpadmin::components.tinymce.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Ftinymce%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.tinymce.index"}, {"name": "5x admin::components.button.index", "param_count": null, "params": [], "start": **********.469502, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/button/index.blade.phpadmin::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "admin::components.button.index"}, {"name": "4x admin::components.modal.index", "param_count": null, "params": [], "start": **********.469953, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/modal/index.blade.phpadmin::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::components.modal.index"}, {"name": "9x admin::components.form.index", "param_count": null, "params": [], "start": **********.470465, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/form/index.blade.phpadmin::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 9, "name_original": "admin::components.form.index"}, {"name": "1x admin::components.seo.index", "param_count": null, "params": [], "start": **********.478664, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/seo/index.blade.phpadmin::components.seo.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fseo%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.seo.index"}, {"name": "1x admin::catalog.products.edit.images", "param_count": null, "params": [], "start": **********.503874, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/images.blade.phpadmin::catalog.products.edit.images", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fimages.blade.php&line=1", "ajax": false, "filename": "images.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.images"}, {"name": "1x admin::components.media.images", "param_count": null, "params": [], "start": **********.507315, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/media/images.blade.phpadmin::components.media.images", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fimages.blade.php&line=1", "ajax": false, "filename": "images.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.media.images"}, {"name": "2x admin::components.shimmer.image.index", "param_count": null, "params": [], "start": **********.514953, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/image/index.blade.phpadmin::components.shimmer.image.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fimage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin::components.shimmer.image.index"}, {"name": "1x admin::catalog.products.edit.multiimages", "param_count": null, "params": [], "start": **********.528726, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/multiimages.blade.phpadmin::catalog.products.edit.multiimages", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fmultiimages.blade.php&line=1", "ajax": false, "filename": "multiimages.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.multiimages"}, {"name": "1x admin::catalog.products.edit.videos", "param_count": null, "params": [], "start": **********.534483, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/videos.blade.phpadmin::catalog.products.edit.videos", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fvideos.blade.php&line=1", "ajax": false, "filename": "videos.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.videos"}, {"name": "1x admin::components.media.videos", "param_count": null, "params": [], "start": **********.537994, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/media/videos.blade.phpadmin::components.media.videos", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fvideos.blade.php&line=1", "ajax": false, "filename": "videos.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.media.videos"}, {"name": "1x admin::catalog.products.edit.types.configurable", "param_count": null, "params": [], "start": **********.538947, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/types/configurable.blade.phpadmin::catalog.products.edit.types.configurable", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Ftypes%2Fconfigurable.blade.php&line=1", "ajax": false, "filename": "configurable.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.types.configurable"}, {"name": "1x admin::components.dropdown.menu.item", "param_count": null, "params": [], "start": **********.546051, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/dropdown/menu/item.blade.phpadmin::components.dropdown.menu.item", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Fmenu%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.dropdown.menu.item"}, {"name": "4x admin::components.drawer.index", "param_count": null, "params": [], "start": **********.560107, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/drawer/index.blade.phpadmin::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "admin::components.drawer.index"}, {"name": "1x admin::catalog.products.edit.links", "param_count": null, "params": [], "start": **********.727232, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/links.blade.phpadmin::catalog.products.edit.links", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Flinks.blade.php&line=1", "ajax": false, "filename": "links.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.links"}, {"name": "1x admin::components.products.search", "param_count": null, "params": [], "start": **********.728072, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/products/search.blade.phpadmin::components.products.search", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.products.search"}, {"name": "1x admin::catalog.products.edit.price.group", "param_count": null, "params": [], "start": **********.798875, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/price/group.blade.phpadmin::catalog.products.edit.price.group", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fprice%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.price.group"}, {"name": "1x admin::components.shimmer.catalog.products.edit.group-price", "param_count": null, "params": [], "start": **********.799856, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/catalog/products/edit/group-price.blade.phpadmin::components.shimmer.catalog.products.edit.group-price", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fcatalog%2Fproducts%2Fedit%2Fgroup-price.blade.php&line=1", "ajax": false, "filename": "group-price.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.catalog.products.edit.group-price"}, {"name": "1x admin::catalog.products.edit.channels", "param_count": null, "params": [], "start": **********.834118, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/channels.blade.phpadmin::catalog.products.edit.channels", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fchannels.blade.php&line=1", "ajax": false, "filename": "channels.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.channels"}, {"name": "1x admin::catalog.products.edit.categories", "param_count": null, "params": [], "start": **********.834726, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/categories.blade.phpadmin::catalog.products.edit.categories", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::catalog.products.edit.categories"}, {"name": "3x admin::components.shimmer.tree.index", "param_count": null, "params": [], "start": **********.83539, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/tree/index.blade.phpadmin::components.shimmer.tree.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Ftree%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "admin::components.shimmer.tree.index"}, {"name": "1x admin::components.tree.view", "param_count": null, "params": [], "start": **********.843409, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/tree/view.blade.phpadmin::components.tree.view", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Ftree%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.tree.view"}, {"name": "1x admin::components.tree.checkbox", "param_count": null, "params": [], "start": **********.843973, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/tree/checkbox.blade.phpadmin::components.tree.checkbox", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Ftree%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.tree.checkbox"}, {"name": "1x admin::components.layouts.index", "param_count": null, "params": [], "start": **********.845477, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/index.blade.phpadmin::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.index"}, {"name": "1x admin::components.flash-group.index", "param_count": null, "params": [], "start": **********.849274, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/index.blade.phpadmin::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.flash-group.index"}, {"name": "1x admin::components.flash-group.item", "param_count": null, "params": [], "start": **********.8499, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/flash-group/item.blade.phpadmin::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.flash-group.item"}, {"name": "1x admin::components.modal.confirm", "param_count": null, "params": [], "start": **********.850467, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/modal/confirm.blade.phpadmin::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.modal.confirm"}, {"name": "1x admin::components.layouts.header.index", "param_count": null, "params": [], "start": **********.851113, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/header/index.blade.phpadmin::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.header.index"}, {"name": "1x admin::components.shimmer.header.mega-search.products", "param_count": null, "params": [], "start": **********.864385, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/products.blade.phpadmin::components.shimmer.header.mega-search.products", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fproducts.blade.php&line=1", "ajax": false, "filename": "products.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.products"}, {"name": "1x admin::components.shimmer.header.mega-search.orders", "param_count": null, "params": [], "start": **********.864963, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/orders.blade.phpadmin::components.shimmer.header.mega-search.orders", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Forders.blade.php&line=1", "ajax": false, "filename": "orders.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.orders"}, {"name": "1x admin::components.shimmer.header.mega-search.categories", "param_count": null, "params": [], "start": **********.865484, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/categories.blade.phpadmin::components.shimmer.header.mega-search.categories", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.categories"}, {"name": "1x admin::components.shimmer.header.mega-search.customers", "param_count": null, "params": [], "start": **********.865996, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/shimmer/header/mega-search/customers.blade.phpadmin::components.shimmer.header.mega-search.customers", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fheader%2Fmega-search%2Fcustomers.blade.php&line=1", "ajax": false, "filename": "customers.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.shimmer.header.mega-search.customers"}, {"name": "1x admin::components.layouts.sidebar.index", "param_count": null, "params": [], "start": **********.867248, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/sidebar/index.blade.phpadmin::components.layouts.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.sidebar.index"}, {"name": "1x admin::components.layouts.tabs", "param_count": null, "params": [], "start": **********.874808, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/components/layouts/tabs.blade.phpadmin::components.layouts.tabs", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin::components.layouts.tabs"}]}, "queries": {"count": 169, "nb_statements": 169, "nb_visible_statements": 169, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09146000000000004, "accumulated_duration_str": "91.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 69 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.267544, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 0.569}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.27207, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.569, "width_percent": 0.23}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.279662, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0.798, "width_percent": 2.318}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.284137, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 3.116, "width_percent": 0.295}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.28565, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 3.411, "width_percent": 0.164}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.2900279, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 3.575, "width_percent": 0.251}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.2931058, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 3.827, "width_percent": 2.34}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 139}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.296342, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Repository.php:152", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=152", "ajax": false, "filename": "Repository.php", "line": "152"}, "connection": "mlk", "explain": null, "start_percent": 6.167, "width_percent": 0.262}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 51}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.319094, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 6.429, "width_percent": 2.394}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 51}], "start": **********.323196, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 8.824, "width_percent": 2.504}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 51}], "start": **********.3331351, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 11.327, "width_percent": 0.35}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 195 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 51}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3363419, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 11.677, "width_percent": 2.537}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 52}], "start": **********.3419921, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 14.214, "width_percent": 0.295}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 53}], "start": **********.345548, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 14.509, "width_percent": 0.372}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 102}], "start": **********.356072, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 14.881, "width_percent": 3.094}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 122}], "start": **********.362547, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 17.975, "width_percent": 0.262}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.365772, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 18.237, "width_percent": 0.35}, {"sql": "select * from `attribute_groups` where `attribute_groups`.`attribute_family_id` = 2 and `attribute_groups`.`attribute_family_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 204}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.3679101, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "f370fcd6fa9a312d66e8060ad83bdbe0:204", "source": {"index": 20, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fstorage%2Fframework%2Fviews%2Ff370fcd6fa9a312d66e8060ad83bdbe0.php&line=204", "ajax": false, "filename": "f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": "204"}, "connection": "mlk", "explain": null, "start_percent": 18.587, "width_percent": 0.339}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 546}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 23, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 216}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3719208, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 18.926, "width_percent": 2.548}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 8 and `code` not in ('color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "type": "query", "params": [], "bindings": ["en", 8, "color", "price", "cost", "special_price", "special_price_from", "special_price_to", "length", "width", "height", "weight", "manage_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 569}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 17, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 216}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.375527, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:569", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=569", "ajax": false, "filename": "AbstractType.php", "line": "569"}, "connection": "mlk", "explain": null, "start_percent": 21.474, "width_percent": 2.318}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 9}], "start": **********.392472, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 23.792, "width_percent": 0.437}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 470}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 26, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 9}], "start": **********.395186, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 24.229, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 9}], "start": **********.3997529, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 24.426, "width_percent": 0.284}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 9}], "start": **********.4043849, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 24.71, "width_percent": 0.273}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.cascading-select-control", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/cascading-select-control.blade.php", "line": 6}], "start": **********.411213, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 24.984, "width_percent": 0.251}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 25 and `attribute_options`.`attribute_id` is not null and `parent_id` is null order by `sort_order` asc", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "557fb6e8131ef703e2e7cfa48dd2e77b", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\557fb6e8131ef703e2e7cfa48dd2e77b.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4127579, "duration": 0.004940000000000001, "duration_str": "4.94ms", "memory": 0, "memory_str": null, "filename": "557fb6e8131ef703e2e7cfa48dd2e77b:17", "source": {"index": 16, "namespace": "view", "name": "557fb6e8131ef703e2e7cfa48dd2e77b", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\557fb6e8131ef703e2e7cfa48dd2e77b.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fstorage%2Fframework%2Fviews%2F557fb6e8131ef703e2e7cfa48dd2e77b.php&line=17", "ajax": false, "filename": "557fb6e8131ef703e2e7cfa48dd2e77b.php", "line": "17"}, "connection": "mlk", "explain": null, "start_percent": 25.235, "width_percent": 5.401}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 185}], "start": **********.423246, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 30.636, "width_percent": 0.273}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 33 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.424622, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "admin::catalog.products.edit.controls:198", "source": {"index": 16, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fcontrols.blade.php&line=198", "ajax": false, "filename": "controls.blade.php", "line": "198"}, "connection": "mlk", "explain": null, "start_percent": 30.91, "width_percent": 0.405}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 9}], "start": **********.432184, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 31.314, "width_percent": 0.284}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 142}], "start": **********.436957, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 31.599, "width_percent": 0.437}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 34 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 154}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.438561, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "admin::catalog.products.edit.controls:154", "source": {"index": 16, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 154}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fcontrols.blade.php&line=154", "ajax": false, "filename": "controls.blade.php", "line": "154"}, "connection": "mlk", "explain": null, "start_percent": 32.036, "width_percent": 0.262}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 9 and `code` not in ('color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "type": "query", "params": [], "bindings": ["en", 9, "color", "color", "price", "cost", "special_price", "special_price_from", "special_price_to", "length", "width", "height", "weight", "manage_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 569}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 17, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 216}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4413319, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:569", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=569", "ajax": false, "filename": "AbstractType.php", "line": "569"}, "connection": "mlk", "explain": null, "start_percent": 32.298, "width_percent": 0.459}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 70}], "start": **********.4451928, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 32.757, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 70}], "start": **********.4572, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 33.009, "width_percent": 0.612}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = 'en' where `attribute_group_mappings`.`attribute_group_id` = 10 and `code` not in ('color', 'color', 'color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock') order by `pivot_position` asc", "type": "query", "params": [], "bindings": ["en", 10, "color", "color", "color", "price", "cost", "special_price", "special_price_from", "special_price_to", "length", "width", "height", "weight", "manage_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 569}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 17, "namespace": "view", "name": "f370fcd6fa9a312d66e8060ad83bdbe0", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f370fcd6fa9a312d66e8060ad83bdbe0.php", "line": 216}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.476784, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:569", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 569}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=569", "ajax": false, "filename": "AbstractType.php", "line": "569"}, "connection": "mlk", "explain": null, "start_percent": 33.621, "width_percent": 0.667}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 70}], "start": **********.481884, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 34.288, "width_percent": 0.426}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 70}], "start": **********.490879, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 34.715, "width_percent": 0.328}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.controls", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/controls.blade.php", "line": 70}], "start": **********.49826, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 35.043, "width_percent": 0.547}, {"sql": "select * from `product_images` where `product_images`.`product_id` = 195 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": "view", "name": "admin::catalog.products.edit.images", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/images.blade.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.504399, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 35.589, "width_percent": 2.132}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `type` = 'multiimage'", "type": "query", "params": [], "bindings": [2, "multiimage"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "admin::catalog.products.edit.multiimages", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/multiimages.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.52927, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "admin::catalog.products.edit.multiimages:5", "source": {"index": 15, "namespace": "view", "name": "admin::catalog.products.edit.multiimages", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/multiimages.blade.php", "line": 5}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FResources%2Fviews%2Fcatalog%2Fproducts%2Fedit%2Fmultiimages.blade.php&line=5", "ajax": false, "filename": "multiimages.blade.php", "line": "5"}, "connection": "mlk", "explain": null, "start_percent": 37.721, "width_percent": 0.459}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": "view", "name": "admin::catalog.products.edit.multiimages", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/multiimages.blade.php", "line": 33}], "start": **********.5321312, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 38.181, "width_percent": 0.295}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` = 195 and `product_videos`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": "view", "name": "admin::catalog.products.edit.videos", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/videos.blade.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.5350041, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 38.476, "width_percent": 2.22}, {"sql": "select * from `inventory_sources` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": "view", "name": "admin::catalog.products.edit.types.configurable", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src/resources/views/catalog/products/edit/types/configurable.blade.php", "line": 2}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.540854, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 40.695, "width_percent": 2.154}, {"sql": "select * from `currencies` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 404}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 367}, {"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 380}, {"index": 21, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 483}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5484211, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:404", "source": {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=404", "ajax": false, "filename": "BaseRepository.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 42.849, "width_percent": 2.088}, {"sql": "select * from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.570894, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7:2139", "source": {"index": 16, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fstorage%2Fframework%2Fviews%2Ff83a29e2adc8bc5fc60490050fe0e9a7.php&line=2139", "ajax": false, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": "2139"}, "connection": "mlk", "explain": null, "start_percent": 44.938, "width_percent": 0.372}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.572074, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7:2139", "source": {"index": 21, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fstorage%2Fframework%2Fviews%2Ff83a29e2adc8bc5fc60490050fe0e9a7.php&line=2139", "ajax": false, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": "2139"}, "connection": "mlk", "explain": null, "start_percent": 45.309, "width_percent": 0.175}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (196, 197) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.572949, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7:2139", "source": {"index": 21, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fstorage%2Fframework%2Fviews%2Ff83a29e2adc8bc5fc60490050fe0e9a7.php&line=2139", "ajax": false, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": "2139"}, "connection": "mlk", "explain": null, "start_percent": 45.484, "width_percent": 0.197}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` in (196, 197)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.5738559, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7:2139", "source": {"index": 21, "namespace": "view", "name": "f83a29e2adc8bc5fc60490050fe0e9a7", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\storage\\framework\\views\\f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": 2139}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fstorage%2Fframework%2Fviews%2Ff83a29e2adc8bc5fc60490050fe0e9a7.php&line=2139", "ajax": false, "filename": "f83a29e2adc8bc5fc60490050fe0e9a7.php", "line": "2139"}, "connection": "mlk", "explain": null, "start_percent": 45.681, "width_percent": 2.121}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5785449, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 47.802, "width_percent": 0.273}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 196 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [196], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5795488, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 48.076, "width_percent": 1.99}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.583653, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 50.066, "width_percent": 0.361}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5861042, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 50.426, "width_percent": 0.317}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.588155, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 50.743, "width_percent": 0.262}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.590123, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 51.006, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.592057, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 51.257, "width_percent": 0.284}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.594061, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 51.542, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.595869, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 51.771, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.597616, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 51.979, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5994968, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 52.176, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.601335, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 52.405, "width_percent": 0.241}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.603241, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 52.646, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.60508, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 52.876, "width_percent": 0.241}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.607085, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 53.116, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.608895, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 53.324, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.610801, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 53.553, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.61264, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 53.761, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.614457, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 53.958, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.616343, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 54.155, "width_percent": 0.241}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6182659, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 54.395, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.620048, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 54.614, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.621816, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 54.844, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.623627, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 55.062, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.625459, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 55.27, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.627251, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 55.478, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.629022, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 55.686, "width_percent": 0.262}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.630964, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 55.948, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6328769, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 56.156, "width_percent": 0.415}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.635011, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 56.571, "width_percent": 0.284}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.636873, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 56.855, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.638772, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 57.063, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.640643, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 57.282, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.643405, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 57.533, "width_percent": 0.23}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 197 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.644281, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 57.763, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.646261, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 58.014, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.648442, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 58.266, "width_percent": 0.426}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6509678, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 58.692, "width_percent": 0.262}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.652988, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 58.955, "width_percent": 0.262}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.655029, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 59.217, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.657024, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 59.447, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.658883, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 59.665, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.66068, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 59.873, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.662489, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 60.07, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6642609, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 60.278, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.666172, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 60.507, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.667984, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 60.726, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6699378, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 60.956, "width_percent": 0.241}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.671798, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 61.196, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6738129, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 61.448, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.675732, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 61.666, "width_percent": 0.241}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 516}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.677651, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 61.907, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679587, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.115, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681046, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.344, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682561, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.574, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684041, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.825, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68552, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 63.055, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686965, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 63.284, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6884189, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 63.481, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689892, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 63.7, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691261, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 63.908, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.692659, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 64.115, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.694011, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 64.312, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695357, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 64.509, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.696719, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 64.717, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6981008, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 64.914, "width_percent": 0.262}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69901, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.176, "width_percent": 0.284}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699483, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.46, "width_percent": 0.208}, {"sql": "select * from `attributes` where `attributes`.`id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699939, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.668, "width_percent": 0.197}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 495, 496, 497, 498)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700537, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.865, "width_percent": 2.176}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = ? and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7047129, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 68.041, "width_percent": 1.968}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = ? and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.709081, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 70.009, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7302809, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 70.206, "width_percent": 0.405}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7324579, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 70.61, "width_percent": 0.273}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7345338, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 70.883, "width_percent": 0.284}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736307, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.168, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.737911, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.397, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739477, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.616, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.740996, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.813, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74247, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.042, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.743906, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.239, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745336, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.436, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.746797, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.633, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748235, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.83, "width_percent": 0.241}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7498941, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.07, "width_percent": 0.361}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751961, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.431, "width_percent": 0.295}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.753954, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.726, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755733, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.945, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75748, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 74.164, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.759169, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 74.371, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760662, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 74.568, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762148, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 74.787, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.763572, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 74.995, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765025, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.191, "width_percent": 0.251}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7665799, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.443, "width_percent": 0.23}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768236, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.672, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.769752, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.88, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771198, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.088, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7726889, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.296, "width_percent": 0.219}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.774173, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.514, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.775702, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.722, "width_percent": 0.295}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.777262, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.017, "width_percent": 0.208}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.778684, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.225, "width_percent": 0.197}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.780088, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.422, "width_percent": 0.23}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` = ? and `attribute_translations`.`attribute_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.782469, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.651, "width_percent": 0.306}, {"sql": "select `products`.*, `product_up_sells`.`parent_id` as `pivot_parent_id`, `product_up_sells`.`child_id` as `pivot_child_id` from `products` inner join `product_up_sells` on `products`.`id` = `product_up_sells`.`child_id` where `product_up_sells`.`parent_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.78336, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.958, "width_percent": 2.11}, {"sql": "select `products`.*, `product_cross_sells`.`parent_id` as `pivot_parent_id`, `product_cross_sells`.`child_id` as `pivot_child_id` from `products` inner join `product_cross_sells` on `products`.`id` = `product_cross_sells`.`child_id` where `product_cross_sells`.`parent_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.785481, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 80.068, "width_percent": 1.859}, {"sql": "select `products`.*, `product_relations`.`parent_id` as `pivot_parent_id`, `product_relations`.`child_id` as `pivot_child_id` from `products` inner join `product_relations` on `products`.`id` = `product_relations`.`child_id` where `product_relations`.`parent_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7873762, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 81.927, "width_percent": 1.837}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = ? where `attribute_group_mappings`.`attribute_group_id` = ? and `code` not in (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) order by `pivot_position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.789516, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 83.763, "width_percent": 2.056}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.79454, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 85.819, "width_percent": 0.241}, {"sql": "select * from `tax_categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.795358, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 86.059, "width_percent": 1.815}, {"sql": "select * from `customer_groups`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.807099, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.874, "width_percent": 2.088}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = ? and `product_customer_group_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.809494, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.963, "width_percent": 1.935}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = ? where `attribute_group_mappings`.`attribute_group_id` = ? and `code` not in (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) order by `pivot_position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.811738, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.898, "width_percent": 0.372}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = ? where `attribute_group_mappings`.`attribute_group_id` = ? and `code` not in (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) order by `pivot_position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.812369, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.27, "width_percent": 0.372}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.81622, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.642, "width_percent": 0.339}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8215709, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.981, "width_percent": 0.273}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.831025, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.254, "width_percent": 0.23}, {"sql": "select `attributes`.*, `attribute_translations`.`name` as `admin_name`, `attribute_translations`.`locale`, `attribute_group_mappings`.`attribute_group_id` as `pivot_attribute_group_id`, `attribute_group_mappings`.`attribute_id` as `pivot_attribute_id`, `attribute_group_mappings`.`position` as `pivot_position` from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` left join `attribute_translations` on `attributes`.`id` = `attribute_translations`.`attribute_id` and `attribute_translations`.`locale` = ? where `attribute_group_mappings`.`attribute_group_id` = ? and `code` not in (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) order by `pivot_position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.833386, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.483, "width_percent": 0.426}, {"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.836644, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.91, "width_percent": 4.253}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (13)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841212, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.163, "width_percent": 1.837}]}, "models": {"data": {"Webkul\\Core\\Models\\Locale": {"value": 586, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 140, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 57, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductAttributeValue": {"value": 57, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOptionTranslation": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOptionTranslation.php&line=1", "ajax": false, "filename": "AttributeOptionTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeTranslation": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeTranslation.php&line=1", "ajax": false, "filename": "AttributeTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeGroup": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeGroup.php&line=1", "ajax": false, "filename": "AttributeGroup.php", "line": "?"}}, "Webkul\\Category\\Models\\CategoryTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventory": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventory.php&line=1", "ajax": false, "filename": "ProductInventory.php", "line": "?"}}, "Webkul\\Tax\\Models\\TaxCategory": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTax%2Fsrc%2FModels%2FTaxCategory.php&line=1", "ajax": false, "filename": "TaxCategory.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Inventory\\Models\\InventorySource": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInventory%2Fsrc%2FModels%2FInventorySource.php&line=1", "ajax": false, "filename": "InventorySource.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Category\\Models\\Category": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}}, "count": 951, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/admin/catalog/products/edit/195?channel=default&locale=en", "action_name": "admin.catalog.products.edit", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@edit", "uri": "GET admin/catalog/products/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@edit<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=137\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/catalog/products", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=137\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php:137-142</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "933ms", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1322161104 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322161104\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-312189121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-312189121\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-435228300 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6Ikh2UGoyTGdiK1FDajZIRGg2SGswVXc9PSIsInZhbHVlIjoiN29GSC95MHRyZWRQamN0ZHg4NFg2bi85RFdIQzIxWURuUGtjRFVhWUVvbDdqekhtaElLSHV0R2R6SXQ4VjRZeiIsIm1hYyI6IjQ0YjUzZWU2MTY2MjZiODVjMWE1NWYyZGNlMjFhYjE4ZjBiZDI0MjI4ZWNlMmUwODJkZGI3ZDkwMzExMmNhZDEiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Im9QSzNCMlV0VFc0cWluenBRR1M2WlE9PSIsInZhbHVlIjoibzlMK1NteUNNNU93MXhLdENySVI4aEt4cDN2bVBVMTFSNjVIL2V6d2U3YkEyakJmK2Jxdk4ycUpVOUhFcStQc1JlNm5GNVpyaVE0d1NBUnRpcGpNUlZUeHFNUWhnaDBEVk82NUR3TUhVTnVlSmtVVEtYTXhqQmZub0lWUTRCRFoiLCJtYWMiOiJiYWQ1ZGM4ZmFkYjQyNDViNmZiNTJjNTBlZjZlMjIzYTU3YzQ5MWVhNzM2NWM1NzJhZjA2OGQzNWRlNzY3M2Q2IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IllGMzNSWGY3Z3F0ZUxhSkp0eHBzeUE9PSIsInZhbHVlIjoiOTMzcWRndytpY2Yzd1dBTW9yUkFXZnZaNStZcGp4Ym9HMG9qM212Vkhpc2Y2S1ptc25MSWV6NlJocHZXM3paWHBDSHg3MnA2Q29Eay9mT2pVNkUvWlNjV1R5eXBkdElpSVpvamk5Rnk0dFoxSTlHMlkwelhRcElqdlBVZUNOZHoiLCJtYWMiOiJmYWJkMDYyMjVlOWM1YmYwYjA1ZjFmZjI2NTU4NWRjYmJlYjE2ODY0YjRjMjI5MjFlMjQ1N2QwMmE2NzYzMTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://mlk.test/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435228300\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-806707489 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e5YacH4ZNAhpDTzx25DgHN8UyVRMJjYNF88EpRnl</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q2qHhbFsQk6yemvG846tEJZwjPiTsQTB9MSPH1RP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806707489\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1287927598 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 08:35:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287927598\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1688553954 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e5YacH4ZNAhpDTzx25DgHN8UyVRMJjYNF88EpRnl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://mlk.test/admin/catalog/products/edit/195?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688553954\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/admin/catalog/products/edit/195?channel=default&locale=en", "action_name": "admin.catalog.products.edit", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@edit"}, "badge": null}}