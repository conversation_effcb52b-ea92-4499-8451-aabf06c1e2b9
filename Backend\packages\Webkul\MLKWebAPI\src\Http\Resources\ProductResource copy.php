<?php

namespace Webkul\MLKWebAPI\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Product\Helpers\Review;

class ProductResource extends JsonResource
{
    /**
     * Review helper instance
     *
     * @var \Webkul\Product\Helpers\Review
     */
    protected $reviewHelper;

    /**
     * Create a new resource instance.
     *
     * @param  mixed  $resource
     * @return void
     */
    public function __construct($resource)
    {
        $this->reviewHelper = app(Review::class);

        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if (!$this->resource) {
            return [];
        }

        $productTypeInstance = $this->getTypeInstance();
        
        return [
            'id'                 => $this->id,
            'type'               => $this->type,
            'name'               => $this->name,
            'url_key'            => $this->url_key,
            'sku'                => $this->sku,
            'product_number'     => $this->product_number,
            'description'        => $this->description,
            'short_description'  => strip_tags($this->short_description),
            
            // 使用Bagisto内置helper处理图片
            'base_image'         => product_image()->getProductBaseImage($this),
            'images'             => product_image()->getGalleryImages($this),
            
            // 状态标识
            'new'                => $this->new,
            'featured'           => $this->featured,
            'visible_individually' => $this->visible_individually,
            'status'             => $this->status,
            'is_new'             => (bool) $this->new,
            'is_featured'        => (bool) $this->featured,
            'on_sale'            => (bool) $productTypeInstance->haveDiscount(),
            'is_saleable'        => (bool) $productTypeInstance->isSaleable(),
            'is_in_wishlist'     => (bool) $this->getOptimizedWishlistStatus(),
            
            // 库存相关
            'in_stock'           => $this->getOptimizedStockStatus(),
            'show_quantity_changer' => $this->show_quantity_changer,
            
            // 价格相关 - 使用TypeInstance统一处理
            'min_price'          => core()->formatPrice($productTypeInstance->getMinimalPrice()),
            'prices'             => $productTypeInstance->getProductPrices(),
            'price_html'         => $productTypeInstance->getPriceHtml(),
            'special_price'      => $this->special_price,
            'formatted_special_price' => $this->special_price ? core()->currency($this->special_price) : null,
            
            // 评价信息 - 使用Review helper
            'ratings'            => [
                'average' => $this->reviewHelper->getAverageRating($this),
                'total'   => $this->reviewHelper->getTotalRating($this),
            ],
            'reviews'            => [
                'total'   => $this->reviewHelper->getTotalReviews($this),
            ],

            // 分类信息
            'categories'         => $this->getOptimizedCategories(),
            
            // 属性信息
            'attributes'         => $this->getOptimizedAttributes(),
            'attribute_family'   => $this->attribute_family ? [
                'id'   => $this->attribute_family->id,
                'name' => $this->attribute_family->name,
                'code' => $this->attribute_family->code,
            ] : null,
            
            // 视频信息
            'videos'             => $this->videos->map(function ($video) {
                return [
                    'id'   => $video->id,
                    'url'  => $video->url,
                    'type' => $video->type,
                ];
            }),
            
            // 扩展字段
            'color'              => $this->getOptimizedColorValue(),
            'discount'           => $this->getOptimizedDiscountInfo($productTypeInstance),
            
            // 可配置产品专用字段
            'variants'           => $this->type == 'configurable' ? $this->getOptimizedVariants() : [],
            'super_attributes'   => $this->type == 'configurable' ? $this->getOptimizedSuperAttributes() : [],
        ];
    }

    /**
     * 获取优化的Wishlist状态 - 基于Shop模块的实现
     *
     * @return bool
     */
    protected function getOptimizedWishlistStatus()
    {
        // 使用Shop模块的优化策略：利用用户的预加载关系
        $user = auth()->guard('sanctum')->user();
        
        if (!$user) {
            return false;
        }
        
        // 如果可以使用Shop模块的wishlist检查策略
        try {
            if (isset($user->wishlist_items)) {
                return $user->wishlist_items
                    ->where('channel_id', core()->getCurrentChannel()->id)
                    ->where('product_id', $this->id)
                    ->count() > 0;
            }
        } catch (\Exception $e) {
            // 如果用户模型没有wishlist_items关系，继续下面的逻辑
        }
        
        // 优先使用预加载的wishlist状态
        if ($this->relationLoaded('user_wishlist_status')) {
            return $this->getRelation('user_wishlist_status');
        }
        
        // 作为备选方案，返回false（应该在控制器层预加载数据）
        return false;
    }

    /**
     * 获取优化的库存状态
     *
     * @return bool
     */
    protected function getOptimizedStockStatus()
    {
        try {
            // 如果预加载了inventory_indices，使用预加载的数据
            if ($this->relationLoaded('inventory_indices') && $this->inventory_indices->isNotEmpty()) {
                $totalQty = $this->inventory_indices->sum('qty');
                return $totalQty >= 1;
            }
            
            // 否则使用产品的默认方法（这应该很少发生）
            return $this->haveSufficientQuantity(1);
        } catch (\Exception $e) {
            logger()->warning('Failed to check product stock status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取优化的分类信息
     *
     * @return array
     */
    protected function getOptimizedCategories()
    {
        if (!$this->relationLoaded('categories')) {
            return [];
        }
        
        return $this->categories->map(function ($category) {
            return [
                'id'   => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'url'  => $category->url ?? route('shop.product_or_category.index', $category->slug),
            ];
        })->toArray();
    }

    /**
     * 获取优化的属性信息
     *
     * @return array
     */
    protected function getOptimizedAttributes()
    {
        if (!$this->relationLoaded('attribute_values')) {
            return [];
        }
        
        $attributes = [];
        $attributeValues = $this->attribute_values->groupBy('attribute_id');
        
        foreach ($attributeValues as $attributeId => $attributeValueGroup) {
            $attributeValue = $attributeValueGroup->first();
            $attribute = $attributeValue->attribute;
            
            if ($attribute && $attribute->is_visible_on_front) {
                $attributes[] = [
                    'id'            => $attribute->id,
                    'code'          => $attribute->code,
                    'name'          => $attribute->name,
                    'type'          => $attribute->type,
                    'value'         => $attributeValue->value ?? null,
                    'label'         => $attributeValue->label ?? null,
                    'is_filterable' => $attribute->is_filterable,
                ];
            }
        }
        
        return $attributes;
    }

    /**
     * 获取优化的颜色值
     *
     * @return array|null
     */
    protected function getOptimizedColorValue()
    {
        try {
            // 对于configurable产品，获取所有变体的color值
            if ($this->type === 'configurable') {
                return $this->getConfigurableProductColors();
            } else {
                // 对于simple产品，直接获取color属性值
                return $this->getSimpleProductColor();
            }
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取优化的折扣信息
     *
     * @param mixed $productTypeInstance
     * @return array
     */
    protected function getOptimizedDiscountInfo($productTypeInstance)
    {
        try {
            // 获取原价和最终价格
            $regularPrice = (float) ($this->price ?: 0);
            $finalPrice = (float) $productTypeInstance->getFinalPrice();
            
            // 确保价格为正数
            $regularPrice = max(0, $regularPrice);
            $finalPrice = max(0, $finalPrice);
            
            // 计算折扣金额和百分比
            $discountAmount = max(0, $regularPrice - $finalPrice);
            $discountPercentage = $regularPrice > 0 ? round(($discountAmount / $regularPrice) * 100, 0) : 0;
            
            // 检查特价信息
            $specialPrice = $this->special_price ? (float) $this->special_price : null;
            $specialPriceFrom = $this->special_price_from;
            $specialPriceTo = $this->special_price_to;
            
            // 检查特价是否在有效期内
            $isSpecialPriceActive = $specialPrice && core()->isChannelDateInInterval($specialPriceFrom, $specialPriceTo);
            
            // 检查是否有折扣
            $hasDiscount = $discountAmount > 0 || $productTypeInstance->haveDiscount();
            
            return [
                'has_discount' => $hasDiscount,
                'regular_price' => $regularPrice,
                'formatted_regular_price' => core()->currency($regularPrice),
                'final_price' => $finalPrice,
                'formatted_final_price' => core()->currency($finalPrice),
                'discount_amount' => $discountAmount,
                'formatted_discount_amount' => core()->currency($discountAmount),
                'discount_percentage' => $discountPercentage,
                'special_price' => $specialPrice,
                'formatted_special_price' => $specialPrice ? core()->currency($specialPrice) : null,
                'special_price_from' => $specialPriceFrom,
                'special_price_to' => $specialPriceTo,
                'is_special_price_active' => $isSpecialPriceActive,
            ];
        } catch (\Exception $e) {
            // 如果获取折扣信息失败，返回基本信息
            $regularPrice = (float) ($this->price ?: 0);
            
            return [
                'has_discount' => false,
                'regular_price' => $regularPrice,
                'formatted_regular_price' => core()->currency($regularPrice),
                'final_price' => $regularPrice,
                'formatted_final_price' => core()->currency($regularPrice),
                'discount_amount' => 0,
                'formatted_discount_amount' => core()->currency(0),
                'discount_percentage' => 0,
                'special_price' => null,
                'formatted_special_price' => null,
                'special_price_from' => null,
                'special_price_to' => null,
                'is_special_price_active' => false,
            ];
        }
    }

    /**
     * 获取优化的变体信息
     *
     * @return array
     */
    protected function getOptimizedVariants()
    {
        if (!$this->relationLoaded('variants')) {
            return [];
        }
        
        return $this->variants->map(function ($variant) {
            // 使用预加载的inventory_indices计算库存状态
            $inStock = false;
            if ($variant->relationLoaded('inventory_indices')) {
                $totalQty = $variant->inventory_indices->sum('qty');
                $inStock = $totalQty >= 1;
            }
            
            return [
                'id'    => $variant->id,
                'sku'   => $variant->sku,
                'name'  => $variant->name,
                'price' => $variant->price,
                'formatted_price' => core()->currency($variant->price),
                'in_stock' => $inStock,
            ];
        })->toArray();
    }

    /**
     * 获取优化的可配置属性信息
     *
     * @return array
     */
    protected function getOptimizedSuperAttributes()
    {
        if (!$this->relationLoaded('super_attributes')) {
            return [];
        }
        
        return $this->super_attributes->map(function ($attribute) {
            $options = [];
            
            // 使用预加载的options关系
            if ($attribute->relationLoaded('options')) {
                $options = $attribute->options->map(function ($option) {
                    return [
                        'id'    => $option->id,
                        'label' => $option->label,
                        'value' => $option->id,
                    ];
                })->toArray();
            }
            
            return [
                'id'      => $attribute->id,
                'code'    => $attribute->code,
                'name'    => $attribute->name,
                'type'    => $attribute->type,
                'options' => $options,
            ];
        })->toArray();
    }

    /**
     * 获取configurable产品的所有color选项
     *
     * @return array|null
     */
    protected function getConfigurableProductColors()
    {
        $colors = [];
        
        // 检查是否有color超级属性
        if (!$this->relationLoaded('super_attributes')) {
            return null;
        }
        
        $colorAttribute = $this->super_attributes->where('code', 'color')->first();
        
        if (!$colorAttribute) {
            return null;
        }
        
        // 使用预加载的变体产品数据
        if (!$this->relationLoaded('variants')) {
            return null;
        }
        
        // 使用预加载的options关系
        $colorOptions = [];
        if ($colorAttribute->relationLoaded('options')) {
            $colorOptions = $colorAttribute->options->keyBy('id');
        }
        
        foreach ($this->variants as $variant) {
            $colorValue = $variant->color;
            
            if ($colorValue && isset($colorOptions[$colorValue])) {
                $colorOption = $colorOptions[$colorValue];
                
                $colorData = [
                    'option_id' => $colorOption->id,
                    'option_value' => $colorValue,
                    'option_label' => $colorOption->admin_name,
                    'variant_id' => $variant->id,
                ];
                
                // 避免重复的color选项
                $exists = false;
                foreach ($colors as $existingColor) {
                    if ($existingColor['option_id'] == $colorData['option_id']) {
                        $exists = true;
                        break;
                    }
                }
                
                if (!$exists) {
                    $colors[] = $colorData;
                }
            }
        }
        
        return empty($colors) ? null : $colors;
    }

    /**
     * 获取simple产品的color值
     *
     * @return array|null
     */
    protected function getSimpleProductColor()
    {
        $colorValue = $this->color;
        if (!$colorValue) {
            return null;
        }
        
        // 尝试从产品的属性值中获取color属性信息
        $colorAttribute = null;
        $colorOption = null;
        
        if ($this->relationLoaded('attribute_values')) {
            $colorAttributeValue = $this->attribute_values->where('attribute.code', 'color')->first();
            if ($colorAttributeValue && $colorAttributeValue->relationLoaded('attribute')) {
                $colorAttribute = $colorAttributeValue->attribute;
                
                // 如果是select类型属性且预加载了options，获取选项标签
                if ($colorAttribute->type === 'select' && $colorAttribute->relationLoaded('options')) {
                    $colorOption = $colorAttribute->options->where('id', $colorValue)->first();
                }
            }
        }
        
        if ($colorOption) {
            return [[
                'option_id' => $colorOption->id,
                'option_value' => $colorValue,
                'option_label' => $colorOption->admin_name,
                'variant_id' => $this->id,
            ]];
        }
        
        // 对于其他类型的属性，直接返回值
        return [[
            'option_value' => $colorValue,
            'option_label' => $colorValue,
            'variant_id' => $this->id,
        ]];
    }
} 