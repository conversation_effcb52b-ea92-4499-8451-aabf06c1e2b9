# 图片搜索API文档

## 概述
图片搜索API允许用户上传图片来搜索相似的产品，提供以图搜图的功能。

## 基础信息
- **接口地址**: `/api/mlk/search/upload-image`
- **请求方式**: `POST`
- **认证要求**: 无需认证（公开接口）
- **中间件**: `api_locale`, `api_auth`
- **内容类型**: `multipart/form-data`

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 限制 |
|--------|------|------|------|------|
| image | file | 是 | 要搜索的图片文件 | JPEG、JPG、PNG、WEBP格式，最大2MB |

## 请求示例

### cURL示例
```bash
curl -X POST "https://example.com/api/mlk/search/upload-image" \
  -H "Accept: application/json" \
  -H "X-Requested-With: XMLHttpRequest" \
  -F "image=@/path/to/image.jpg"
```

### JavaScript FormData示例
```javascript
const formData = new FormData();
formData.append('image', imageFile);

fetch('/api/mlk/search/upload-image', {
    method: 'POST',
    headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('图片上传成功:', data.data.image_url);
    }
});
```

### HTML表单示例
```html
<form action="/api/mlk/search/upload-image" method="POST" enctype="multipart/form-data">
    <input type="file" name="image" accept=".jpg,.jpeg,.png,.webp" required>
    <button type="submit">上传图片搜索</button>
</form>
```

## 响应格式

### 成功响应
```json
{
    "success": true,
    "message": "图片上传成功",
    "data": {
        "image_url": "https://example.com/storage/search/images/2024/01/15/abc123def456.jpg"
    }
}
```

### 错误响应

#### 文件格式错误
```json
{
    "success": false,
    "message": "文件验证失败",
    "errors": {
        "image": [
            "图片必须是 jpeg, jpg, png, webp 格式的文件"
        ]
    }
}
```

#### 文件大小超限
```json
{
    "success": false,
    "message": "文件验证失败", 
    "errors": {
        "image": [
            "图片大小不能超过 2MB"
        ]
    }
}
```

#### 未选择文件
```json
{
    "success": false,
    "message": "文件验证失败",
    "errors": {
        "image": [
            "请选择要上传的图片文件"
        ]
    }
}
```

#### 服务器错误
```json
{
    "success": false,
    "message": "图片处理失败，请稍后重试",
    "data": {
        "message": "存储空间不足"
    }
}
```

## 图片要求

### 支持的文件格式
- **JPEG** (.jpg, .jpeg)
- **PNG** (.png)
- **WebP** (.webp)

### 文件大小限制
- **最大文件大小**: 2MB (2048KB)
- **建议尺寸**: 不超过 1920x1920 像素
- **最小尺寸**: 建议不小于 200x200 像素

### 图片质量建议
1. **清晰度**: 图片应该清晰，避免模糊或过度压缩
2. **光线**: 光线充足，避免过暗或过亮
3. **角度**: 正面拍摄效果更佳
4. **背景**: 简洁的背景有助于提高识别准确度
5. **完整性**: 确保产品完整出现在图片中

## 使用流程

### 1. 前端实现示例
```javascript
class ImageSearch {
    constructor() {
        this.apiUrl = '/api/mlk/search/upload-image';
        this.maxFileSize = 2 * 1024 * 1024; // 2MB
        this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    }
    
    // 验证文件
    validateFile(file) {
        const errors = [];
        
        if (!file) {
            errors.push('请选择图片文件');
            return errors;
        }
        
        if (!this.allowedTypes.includes(file.type)) {
            errors.push('仅支持 JPEG、PNG、WebP 格式的图片');
        }
        
        if (file.size > this.maxFileSize) {
            errors.push('图片大小不能超过 2MB');
        }
        
        return errors;
    }
    
    // 上传图片
    async uploadImage(file) {
        const errors = this.validateFile(file);
        if (errors.length > 0) {
            throw new Error(errors.join(', '));
        }
        
        const formData = new FormData();
        formData.append('image', file);
        
        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            });
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '上传失败');
            }
            
            return result.data.image_url;
        } catch (error) {
            console.error('图片上传失败:', error);
            throw error;
        }
    }
}

// 使用示例
const imageSearch = new ImageSearch();
const fileInput = document.getElementById('image-input');

fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    
    try {
        const imageUrl = await imageSearch.uploadImage(file);
        console.log('上传成功，图片URL:', imageUrl);
        
        // 可以将图片URL用于后续的搜索操作
        // 例如：调用搜索API，使用图片URL作为参数
        
    } catch (error) {
        console.error('上传失败:', error.message);
        alert('图片上传失败: ' + error.message);
    }
});
```

### 2. 图片预览功能
```javascript
// 添加图片预览功能
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const preview = document.getElementById('image-preview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}
```

### 3. 拖拽上传功能
```javascript
// 拖拽上传实现
function setupDragDrop() {
    const dropZone = document.getElementById('drop-zone');
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('drag-over');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('drag-over');
    });
    
    dropZone.addEventListener('drop', async (e) => {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            try {
                const imageUrl = await imageSearch.uploadImage(files[0]);
                console.log('拖拽上传成功:', imageUrl);
            } catch (error) {
                console.error('拖拽上传失败:', error);
            }
        }
    });
}
```

## 安全注意事项

1. **文件类型验证**: 服务器会严格验证文件MIME类型
2. **文件大小限制**: 防止大文件上传导致服务器资源耗尽
3. **病毒扫描**: 建议对上传的文件进行病毒扫描
4. **存储安全**: 上传的图片存储在安全的目录中
5. **访问控制**: 图片URL具有一定的随机性，防止恶意访问

## 性能优化建议

1. **图片压缩**: 前端可先对图片进行适当压缩再上传
2. **缓存策略**: 对已上传的图片实施适当的缓存策略
3. **CDN加速**: 建议使用CDN来加速图片访问
4. **异步处理**: 大图片可考虑异步处理机制

## 常见问题

### Q: 上传后的图片URL是否永久有效？
A: 图片URL有一定的生命周期，建议及时使用。具体的保存期限请咨询系统管理员。

### Q: 是否支持批量上传？
A: 当前API仅支持单张图片上传，如需批量上传请多次调用。

### Q: 上传失败后如何处理？
A: 检查图片格式、大小是否符合要求，网络是否正常。可以重试上传。

### Q: 支持哪些图片搜索算法？
A: 具体的图片搜索算法由后端SearchRepository实现，支持基于特征的相似度匹配。 