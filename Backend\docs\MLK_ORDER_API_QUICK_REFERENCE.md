# MLK 订单 API 快速参考

## 🚀 新增订单管理接口

### 📋 接口概览
| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 订单列表 | `GET` | `/api/mlk/orders` | 分页获取用户订单 |
| 订单详情 | `GET` | `/api/mlk/orders/{id}` | 获取单个订单详情 |
| 重新下单 | `POST` | `/api/mlk/orders/{id}/reorder` | 历史订单商品加入购物车 |
| 取消订单 | `POST` | `/api/mlk/orders/{id}/cancel` | 取消待处理订单 |
| 下载发票 | `GET` | `/api/mlk/orders/{invoice_id}/invoice` | 下载PDF发票 |
| 状态统计 | `GET` | `/api/mlk/orders/stats/status` | 各状态订单数量 |

### 🔐 认证
```http
Authorization: Bearer {token}
```

### 📊 订单状态
- `pending` - 待处理
- `processing` - 处理中  
- `completed` - 已完成
- `canceled` - 已取消
- `closed` - 已关闭
- `fraud` - 欺诈

### 🎯 快速示例

#### 获取订单列表
```bash
GET /api/mlk/orders?page=1&per_page=10&status=pending
```

#### 重新下单
```bash
POST /api/mlk/orders/123/reorder
```

#### 取消订单
```bash
POST /api/mlk/orders/123/cancel
```

#### 下载发票
```bash
GET /api/mlk/orders/456/invoice
```

### ✅ 成功响应格式
```json
{
    "success": true,
    "data": { ... }
}
```

### ❌ 错误响应格式
```json
{
    "success": false,
    "message": "错误信息",
    "code": 404
}
```

### 📝 补全功能对比

| 功能 | Shop模块 | MLKWebAPI | 状态 |
|------|----------|-----------|------|
| 订单列表 | ✅ | ✅ | 已补全 |
| 订单详情 | ✅ | ✅ | 已补全 |
| 重新下单 | ✅ | ✅ | **新增** |
| 取消订单 | ✅ | ✅ | **新增** |
| 打印发票 | ✅ | ✅ | **新增** |
| 状态统计 | ❌ | ✅ | **增强** |

### 🔧 实现文件
- **控制器**: `OrderController.php`
- **路由**: `api.php` 
- **资源类**: `OrderResource.php` (已存在)
- **PDF模板**: `invoice-pdf.blade.php`

---
**📅 更新时间**: 2024-01-15  
**🎯 目标**: 补全 MLKWebAPI 订单管理功能，与 Shop 模块保持对等
