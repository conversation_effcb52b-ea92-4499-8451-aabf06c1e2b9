# 客户订单测试数据生成器 (Customer Order Seeder)

## 概述
`CustomerOrderSeeder` 是专门为 Bagisto 电商系统中客户ID为3的用户生成完整测试订单数据的数据填充器。

## 功能特性

### 🎯 目标客户
- 专门为客户ID为3的用户生成订单数据
- 自动检查客户是否存在，不存在则退出

### 📦 订单生成规则
- 为每个客户生成1-3个订单
- 每个订单包含2-5个随机商品
- 所有数据使用英文（en_US语言环境）

### 🛍️ 完整的订单数据结构
1. **订单主记录** - 包含订单基本信息
2. **收货地址** - 生成美国地址格式的收货信息
3. **订单商品** - 随机选择现有可用商品
4. **价格计算** - 包含商品价格、运费、税费（8%）
5. **支付记录** - 支持货到付款和银行转账
6. **物流信息** - 为已完成订单生成运输记录和追踪号

## 生成的数据包括

### 📋 订单信息
- 订单编号：格式 `ORD-{字母}{6位数字}`
- 订单状态：`pending`、`processing`、`completed`
- 创建时间：过去30天内的随机时间
- 货币：USD
- 运输方式：Flat Rate

### 📍 地址信息
- **收货地址**：随机生成美国地址
- **账单地址**：与收货地址相同或相似

### 🛒 商品信息
- 随机选择2-5个可用商品
- 每个商品数量：1-3件
- 价格范围：$10-$500
- 包含重量、SKU等完整信息

### 💰 价格结构
- **商品小计**：所有商品价格总和
- **运费**：$5-$25随机金额
- **税费**：商品小计的8%
- **订单总额**：小计+运费+税费

### 🚚 物流信息（仅限已完成订单）
- 运输状态：已发货
- 承运商：Flat Rate
- 追踪号：格式 `TR{9位数字}`
- 发货清单：包含所有订单商品

### 💳 支付记录
- 支付方式：货到付款(COD)或银行转账
- 已完成订单包含支付交易记录

## 使用方法

### 运行整个数据库填充
```bash
php artisan db:seed
```

### 仅运行订单数据填充
```bash
php artisan db:seed --class=CustomerOrderSeeder
```

### 运行前确认
```bash
# 检查客户ID 3是否存在
php artisan tinker
>>> \Webkul\Customer\Models\Customer::find(3)
```

## 数据依赖

### 必需的数据
1. **客户ID 3必须存在**
2. **至少有一些可用商品**（status=1, type=simple, 有库存）
3. **默认渠道和库存源存在**

### 自动检查
- Seeder会自动检查客户是否存在
- 自动查找可用商品
- 如果条件不满足会退出并显示错误信息

## 生成的数据表

该 Seeder 会在以下数据表中插入数据：

1. `orders` - 订单主表
2. `addresses` - 订单地址表（收货和账单地址）
3. `order_items` - 订单商品表
4. `order_payment` - 订单支付表
5. `shipments` - 运输表（已完成订单）
6. `shipment_items` - 运输商品表
7. `order_transactions` - 交易记录表（已完成订单）

## 示例输出

运行 Seeder 时你会看到类似的输出：
```
Starting to seed order data for customer ID 3
Found customer: John Doe (<EMAIL>)
Found 15 available products
Creating order 1 of 2...
Creating order 2 of 2...
Order seeding completed successfully!
```

## 注意事项

### ⚠️ 重要提醒
- 这个 Seeder 只为客户ID为3生成数据
- 运行前请确保有足够的可用商品
- 生成的是测试数据，请勿在生产环境使用

### 🔧 自定义选项
如需为其他客户生成数据，请修改 `CustomerOrderSeeder.php` 中的常量：
```php
private const CUSTOMER_ID = 3; // 修改为目标客户ID
```

## 故障排除

### 常见问题
1. **"Customer with ID 3 not found!"**
   - 解决：确保客户ID 3存在于数据库中

2. **"No available products found!"**
   - 解决：运行产品数据填充器或确保有可用商品

3. **运行时错误**
   - 检查数据库连接
   - 确保所有必需的表都存在
   - 检查权限设置 