# 订阅功能 API 快速参考

## 接口总览

| 功能 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 订阅 | POST | `/api/subscribe` | 订阅邮件通知 |
| 取消订阅 | POST | `/api/unsubscribe` | 取消邮件订阅 |
| 查询状态 | POST | `/api/subscription/status` | 查询订阅状态 |

## 订阅类型

- `newsletter` - 新闻通讯（默认）
- `promotional` - 促销信息
- `updates` - 产品更新
- `all` - 所有类型

## 快速示例

### 1. 订阅新闻通讯
```bash
POST /api/subscribe
{
  "email": "<EMAIL>",
  "subscription_type": "newsletter"
}
```

### 2. 取消订阅
```bash
POST /api/unsubscribe
{
  "email": "<EMAIL>",
  "subscription_type": "promotional"
}
```

### 3. 查询状态
```bash
POST /api/subscription/status
{
  "email": "<EMAIL>"
}
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "data": null,
  "errors": { ... }
}
```

## 常见错误

- `400` - 已订阅相同类型
- `404` - 订阅信息不存在
- `422` - 参数验证失败

## 参数说明

### 订阅接口
- `email` (必填) - 邮箱地址
- `subscription_type` (可选) - 订阅类型

### 取消订阅接口
- `email` (条件必填) - 邮箱地址
- `token` (条件必填) - 订阅令牌
- `subscription_type` (可选) - 订阅类型

### 查询状态接口
- `email` (必填) - 邮箱地址
- `subscription_type` (可选) - 订阅类型

> 注意：email 和 token 至少提供一个 