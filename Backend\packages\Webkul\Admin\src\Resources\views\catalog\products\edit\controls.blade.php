@switch($attribute->type)
    @case('text')
        <div class="flex flex-col gap-2.5">
            <div class="flex gap-2.5">
                <v-field
                    type="text"
                    name="{{ $attribute->code }}"
                    :rules="{{ $attribute->validations }}"
                    value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
                    v-slot="{ field }"
                    label="{{ $attribute->admin_name }}"
                >
                    <input
                        type="text"
                        id="{{ $attribute->code }}"
                        :class="[errors['{{ $attribute->code }}'] ? 'border border-red-600 hover:border-red-600' : '']"
                        class="w-full rounded-md border px-3 py-2.5 text-sm text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400"
                        name="{{ $attribute->code }}"
                        v-bind="field"
                        @if ($attribute->code == 'url_key') v-slugify @endif
                        @if ($attribute->code == 'name') v-slugify-target:url_key="setValues" @endif
                    >
                </v-field>
            </div>

            @if ($attribute->code == 'product_number')
                <!-- 使用独立的Vue组件 -->
                <v-sync-inventory></v-sync-inventory>
            @endif
        </div>

        @break
    @case('price')
        <x-admin::form.control-group.control
            type="price"
            :id="$attribute->code"
            :class="($attribute->code == 'price' ? 'py-2.5 bg-gray-50 text-xl font-bold' : '')"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        >
            <x-slot:currency :class="'dark:text-gray-300 ' . ($attribute->code == 'price' ? 'bg-gray-50 dark:bg-gray-900 text-xl' : '')">
                {{ core()->currencySymbol(core()->getBaseCurrencyCode()) }}
            </x-slot>
        </x-admin::form.control-group.control>

        @break
    @case('textarea')
        <x-admin::form.control-group.control
            type="textarea"
            :id="$attribute->code"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
            :tinymce="(bool) $attribute->enable_wysiwyg"
            :prompt="core()->getConfigData('general.magic_ai.content_generation.product_' . $attribute->code . '_prompt')"
        />

        @break
    @case('date')
        <x-admin::form.control-group.control
            type="date"
            :id="$attribute->code"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        />

        @break
    @case('datetime')
        <x-admin::form.control-group.control
            type="datetime"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        />

        @break
    @case('select')
        @if($attribute->code === 'brand' || $attribute->code === 'device' || $attribute->code === 'Device')
            @include('admin::catalog.products.edit.cascading-select-control', ['attribute' => $attribute, 'product' => $product])
        @else
            <x-admin::form.control-group.control
                type="select"
                :id="$attribute->code"
                :name="$attribute->code"
                ::rules="{{ $attribute->validations }}"
                :value="old($attribute->code) ?: $product[$attribute->code]"
                :label="$attribute->admin_name"
            >
                @php
                    $selectedOption = old($attribute->code) ?: $product[$attribute->code];

                    if ($attribute->code != 'tax_category_id') {
                        $options = $attribute->options()->orderBy('sort_order')->get();
                    } else {
                        $options = app('Webkul\Tax\Repositories\TaxCategoryRepository')->all();
                    }
                @endphp

                @foreach ($options as $option)
                    <option
                        value="{{ $option->id }}"
                        {{ $selectedOption == $option->id ? 'selected' : '' }}
                    >
                        {{ $option->admin_name ?? $option->name }}
                    </option>
                @endforeach
            </x-admin::form.control-group.control>
        @endif

        @break
    @case('multiselect')
        @php
            $selectedOption = old($attribute->code) ?: explode(',', $product[$attribute->code]);
        @endphp

        <x-admin::form.control-group.control
            type="multiselect"
            :id="$attribute->code . '[]'"
            :name="$attribute->code . '[]'"
            ::rules="{{ $attribute->validations }}"
            :label="$attribute->admin_name"
        >
            @foreach ($attribute->options()->orderBy('sort_order')->get() as $option)
                <option
                    value="{{ $option->id }}"
                    {{ in_array($option->id, $selectedOption) ? 'selected' : ''}}
                >
                    {{ $option->admin_name }}
                </option>
            @endforeach
        </x-admin::form.control-group.control>

        @break
    @case('checkbox')
        @php
            $selectedOption = old($attribute->code) ?: explode(',', $product[$attribute->code]);
        @endphp

        @foreach ($attribute->options as $option)
            <div class="mb-2 flex items-center gap-2.5 last:!mb-0">
                <x-admin::form.control-group.control
                    type="checkbox"
                    :id="$attribute->code . '_' . $option->id"
                    :name="$attribute->code . '[]'"
                    ::rules="{{ $attribute->validations }}"
                    :value="$option->id"
                    :for="$attribute->code . '_' . $option->id"
                    :label="$attribute->admin_name"
                    :checked="in_array($option->id, $selectedOption)"
                />

                <label
                    class="cursor-pointer select-none text-xs font-medium text-gray-600 dark:text-gray-300"
                    for="{{ $attribute->code . '_' . $option->id }}"
                >
                    {{ $option->admin_name }}
                </label>
            </div>
        @endforeach

        @break
    @case('boolean')
        @php $selectedValue = old($attribute->code) ?: $product[$attribute->code] @endphp

        <x-admin::form.control-group.control
            type="switch"
            :id="$attribute->code"
            :name="$attribute->code"
            :value="1"
            :label="$attribute->admin_name"
            :checked="(boolean) $selectedValue"
        />

        @break
    @case('image')
    @case('file')
        <div class="flex gap-2.5">
            @if ($product[$attribute->code])
                <a
                    href="{{ route('admin.catalog.products.file.download', [$product->id, $attribute->id] )}}"
                    class="flex"
                >
                    @if ($attribute->type == 'image')
                        @if (Storage::exists($product[$attribute->code]))
                            <img
                                src="{{ Storage::url($product[$attribute->code]) }}"
                                class="h-[45px] w-[45px] overflow-hidden rounded border hover:border-gray-400 dark:border-gray-800"
                            />
                        @endif
                    @else
                        <div class="inline-flex w-full max-w-max cursor-pointer appearance-none items-center justify-between gap-x-1 rounded-md border border-transparent p-1.5 text-center text-gray-600 transition-all marker:shadow hover:bg-gray-200 active:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-800">
                            <i class="icon-down-stat text-2xl"></i>
                        </div>
                    @endif
                </a>

                <input
                    type="hidden"
                    name="{{ $attribute->code }}"
                    value="{{ $product[$attribute->code] }}"
                />
            @endif

            <v-field
                type="file"
                class="w-full"
                name="{{ $attribute->code }}"
                :rules="{{ $attribute->validations }}"
                v-slot="{ handleChange, handleBlur }"
                label="{{ $attribute->admin_name }}"
            >
                <input
                    type="file"
                    id="{{ $attribute->code }}"
                    :class="[errors['{{ $attribute->code }}'] ? 'border border-red-600 hover:border-red-600' : '']"
                    class="w-full rounded-md border px-3 py-2.5 text-sm text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:text-gray-300 dark:file:bg-gray-800 dark:file:dark:text-white dark:hover:border-gray-400 dark:focus:border-gray-400"
                    name="{{ $attribute->code }}"
                    @change="handleChange"
                    @blur="handleBlur"
                >
            </v-field>
        </div>

        @if ($product[$attribute->code])
            <div class="mt-2.5 flex items-center gap-2.5">
                <x-admin::form.control-group.control
                    type="checkbox"
                    :id="$attribute->code . '_delete'"
                    :name="$attribute->code . '[delete]'"
                    value="1"
                    :for="$attribute->code . '_delete'"
                />

                <label
                    for="{{ $attribute->code . '_delete' }}"
                    class="cursor-pointer select-none text-sm text-gray-600 dark:text-gray-300"
                >
                    @lang('admin::app.catalog.products.edit.remove')
                </label>
            </div>
        @endif

        @break
    @case('multiimage')
        {{-- multiimage type is handled separately in the images section --}}
        @break
@endswitch

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-sync-inventory-template"
    >
        <div class="flex">
            <button
                type="button"
                class="secondary-button text-sm py-1.5 px-2.5"
                @click="syncInventory"
                :disabled="isLoading"
            >
                @{{ isLoading ? '同步中...' : '@lang('admin::app.catalog.products.edit.sync-inventory')' }}
            </button>
        </div>
    </script>

    <script type="module">
        app.component('v-sync-inventory', {
            template: '#v-sync-inventory-template',

            data() {
                return {
                    isLoading: false,
                }
            },

            methods: {
                /**
                 * 同步库存方法
                 */
                syncInventory() {
                    // 检查产品类型
                    const productType = this.getProductType();
                    console.log('产品类型:', productType);

                    if (productType === 'configurable') {
                        this.syncConfigurableInventory();
                        return;
                    }
                    // 获取Product Number输入框的值
                    const productNumberInput = document.getElementById('product_number');

                    if (!productNumberInput) {
                        console.error('未找到Product Number输入框');
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '未找到Product Number输入框'
                        });
                        return;
                    }

                    const productNumber = productNumberInput.value.trim();

                    if (!productNumber) {
                        console.warn('Product Number为空，无法进行同步');
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: 'Product Number为空，请先填写产品编号'
                        });
                        return;
                    }

                    console.log('开始同步库存，Product Number:', productNumber);

                    this.isLoading = true;

                    // 使用Bagisto的axios实例发起请求
                    this.$axios.post("{{ route('admin.external_api.grid_data_search') }}", {
                            key: productNumber,
                            start: 0,
                            limit: 25
                        })
                        .then(response => {
                            console.log('同步库存响应数据:', response.data);

                            if (response.data.success) {
                                console.log('库存同步成功:', response.data.data);

                                // 解析返回的数据
                                const data = response.data.data;
                                const totalCount = data.totalCount || 0;
                                const topics = data.topics || [];

                                if (topics.length > 0) {
                                    const product = topics[0];

                                    // 构建成功消息
                                    let successMessage = `库存同步成功！\n`;
                                    successMessage += `产品名称: ${product.NameCn || product.NameIt || '未知'}\n`;
                                    successMessage += `产品编码: ${product.Code || '未知'}\n`;
                                    successMessage += `库存数量: ${product.Inventory1 || 0}\n`;
                                    successMessage += `销售价格: ${product.S_Price1 || '0.00'}`;

                                    // 显示成功弹窗
                                    this.$emitter.emit('add-flash', {
                                        type: 'success',
                                        message: successMessage
                                    });

                                    // 自动填充表单字段
                                    this.fillFormFields(product);

                                } else {
                                    this.$emitter.emit('add-flash', {
                                        type: 'warning',
                                        message: '同步成功，但未找到匹配的产品数据'
                                    });
                                }

                            } else {
                                console.error('库存同步失败:', response.data.message || response.data.error);

                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: `库存同步失败: ${response.data.message || response.data.error || '未知错误'}`
                                });
                            }

                            this.isLoading = false;
                        })
                        .catch(error => {
                            console.error('同步库存请求失败:', error);
                            this.isLoading = false;

                            // 处理不同类型的错误
                            let errorMessage = '库存同步失败，请重试';

                            if (error.response) {
                                // 服务器响应了错误状态码
                                const status = error.response.status;
                                const data = error.response.data;

                                switch (status) {
                                    case 404:
                                        errorMessage = '接口不存在，请检查配置';
                                        break;
                                    case 422:
                                        errorMessage = `参数验证失败: ${data.message || '请检查输入参数'}`;
                                        break;
                                    case 500:
                                        errorMessage = '服务器内部错误，请稍后重试';
                                        break;
                                    default:
                                        errorMessage = data.message || `请求失败 (${status})`;
                                }
                            } else if (error.request) {
                                // 请求已发送但没有收到响应
                                errorMessage = '网络连接失败，请检查网络连接';
                            }

                            this.$emitter.emit('add-flash', {
                                type: 'error',
                                message: errorMessage
                            });
                        });
                },

                /**
                 * 填充表单字段
                 */
                fillFormFields(productData) {
                    try {
                        // 映射字段关系
                        const fieldMappings = {
                            'sku': productData.id_no,           // id_no -> sku
                            'group_code': productData.CpflCode, // CpflCode -> group_code
                            'name': productData.NameIt          // NameIt -> name
                        };

                        // 填充字段
                        Object.keys(fieldMappings).forEach(fieldId => {
                            const value = fieldMappings[fieldId];
                            if (value) {
                                this.setFieldValue(fieldId, value);
                            }
                        });

                        console.log('表单字段填充完成');

                    } catch (error) {
                        console.error('填充表单字段时出错:', error);
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: '数据同步成功，但自动填充表单时出现问题'
                        });
                    }
                },

                /**
                 * 设置字段值
                 */
                setFieldValue(fieldId, value) {
                    try {
                        // 方法1: 通过ID直接设置
                        const element = document.getElementById(fieldId);
                        if (element) {
                            // 设置值
                            element.value = value;

                            // 触发input事件，确保Vue能检测到变化
                            const inputEvent = new Event('input', { bubbles: true });
                            element.dispatchEvent(inputEvent);

                            // 触发change事件
                            const changeEvent = new Event('change', { bubbles: true });
                            element.dispatchEvent(changeEvent);

                            console.log(`字段 ${fieldId} 已设置为: ${value}`);
                            return true;
                        }

                        // 方法2: 通过name属性查找
                        const elementByName = document.querySelector(`input[name="${fieldId}"]`);
                        if (elementByName) {
                            elementByName.value = value;

                            // 触发事件
                            const inputEvent = new Event('input', { bubbles: true });
                            elementByName.dispatchEvent(inputEvent);

                            const changeEvent = new Event('change', { bubbles: true });
                            elementByName.dispatchEvent(changeEvent);

                            console.log(`字段 ${fieldId} (通过name) 已设置为: ${value}`);
                            return true;
                        }

                        console.warn(`未找到字段: ${fieldId}`);
                        return false;

                    } catch (error) {
                        console.error(`设置字段 ${fieldId} 时出错:`, error);
                        return false;
                    }
                },

                /**
                 * 获取产品类型
                 */
                getProductType() {
                    try {
                        // 方法1: 从URL中获取产品类型信息
                        const url = window.location.href;
                        if (url.includes('/configurable/')) {
                            return 'configurable';
                        }

                        // 方法2: 检查页面中是否存在configurable相关的Vue组件
                        const configurableComponent = document.querySelector('[data-vv-as="configurable"]') ||
                                                    document.querySelector('v-product-variations') ||
                                                    document.querySelector('#v-product-variations-template');

                        if (configurableComponent) {
                            return 'configurable';
                        }

                        // 方法3: 检查是否存在variants数据
                        if (window.app && window.app.$children) {
                            const productEditComponent = window.app.$children.find(child =>
                                child.variants && Array.isArray(child.variants)
                            );
                            if (productEditComponent && productEditComponent.variants.length > 0) {
                                return 'configurable';
                            }
                        }

                        return 'simple';
                    } catch (error) {
                        console.error('获取产品类型时出错:', error);
                        return 'simple';
                    }
                },

                /**
                 * 同步configurable产品库存
                 */
                syncConfigurableInventory() {
                    // 获取Product Number输入框的值
                    const productNumberInput = document.getElementById('product_number');

                    if (!productNumberInput) {
                        console.error('未找到Product Number输入框');
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '未找到Product Number输入框'
                        });
                        return;
                    }

                    const productNumber = productNumberInput.value.trim();

                    if (!productNumber) {
                        console.warn('Product Number为空，无法进行同步');
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: 'Product Number为空，请先填写产品编号'
                        });
                        return;
                    }

                    // 获取当前变体数据
                    const variants = this.getConfigurableVariants();
                    if (!variants || variants.length === 0) {
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: '当前产品没有变体，请先添加变体'
                        });
                        return;
                    }

                    // 显示变体库存分配弹窗
                    this.showVariantInventoryModal(variants, productNumber);
                },

                /**
                 * 获取configurable产品的变体数据
                 */
                getConfigurableVariants() {
                    try {
                        // 尝试从父组件获取变体数据
                        let variants = null;

                        // 方法1: 从Vue根实例查找
                        if (window.app && window.app.$children) {
                            const productEditComponent = window.app.$children.find(child =>
                                child.variants && Array.isArray(child.variants)
                            );
                            if (productEditComponent) {
                                variants = productEditComponent.variants;
                            }
                        }

                        // 方法2: 从全局变量查找（如果有的话）
                        if (!variants && window.productVariants) {
                            variants = window.productVariants;
                        }

                        // 方法3: 从DOM中的script标签解析
                        if (!variants) {
                            const scriptTags = document.querySelectorAll('script');
                            for (let script of scriptTags) {
                                const content = script.textContent || script.innerText;
                                if (content.includes('variants:') && content.includes('@json')) {
                                    // 尝试解析variants数据
                                    const match = content.match(/variants:\s*(@json\([^)]+\))/);
                                    if (match) {
                                        try {
                                            // 这里需要服务器端渲染的数据，暂时返回空数组
                                            console.log('找到variants定义，但需要服务器端数据');
                                        } catch (e) {
                                            console.error('解析variants数据失败:', e);
                                        }
                                    }
                                }
                            }
                        }

                        console.log('获取到的变体数据:', variants);
                        return variants || [];

                    } catch (error) {
                        console.error('获取变体数据时出错:', error);
                        return [];
                    }
                },

                /**
                 * 显示变体库存分配弹窗
                 */
                showVariantInventoryModal(variants, productNumber) {
                    // 获取颜色属性的变体
                    const colorVariants = this.getColorVariants(variants);

                    if (colorVariants.length === 0) {
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: '未找到颜色变体，无法进行库存分配'
                        });
                        return;
                    }

                    // 创建弹窗HTML
                    this.createVariantInventoryModal(colorVariants, productNumber);
                },

                /**
                 * 获取颜色变体信息
                 */
                getColorVariants(variants) {
                    try {
                        const colorVariants = [];

                        // 假设颜色属性ID为23（根据你的记忆）
                        const colorAttributeId = 23;

                        variants.forEach(variant => {
                            // 查找颜色属性值
                            const colorValue = variant[colorAttributeId] || variant['23'];

                            if (colorValue) {
                                // 获取颜色名称（需要从superAttributes中查找）
                                const colorName = this.getColorName(colorValue);

                                colorVariants.push({
                                    id: variant.id,
                                    colorId: colorValue,
                                    colorName: colorName,
                                    currentInventory: this.getVariantInventory(variant),
                                    ratio: 1 // 默认比例为1
                                });
                            }
                        });

                        console.log('颜色变体:', colorVariants);
                        return colorVariants;

                    } catch (error) {
                        console.error('获取颜色变体时出错:', error);
                        return [];
                    }
                },

                /**
                 * 获取颜色名称
                 */
                getColorName(colorId) {
                    try {
                        // 尝试从superAttributes中获取颜色名称
                        // 这里需要访问父组件的superAttributes数据
                        return `颜色${colorId}`; // 临时返回，实际需要从属性选项中获取
                    } catch (error) {
                        console.error('获取颜色名称时出错:', error);
                        return `颜色${colorId}`;
                    }
                },

                /**
                 * 获取变体当前库存
                 */
                getVariantInventory(variant) {
                    try {
                        if (variant.inventories) {
                            // 计算总库存
                            let totalInventory = 0;
                            Object.values(variant.inventories).forEach(qty => {
                                totalInventory += parseInt(qty) || 0;
                            });
                            return totalInventory;
                        }
                        return 0;
                    } catch (error) {
                        console.error('获取变体库存时出错:', error);
                        return 0;
                    }
                },

                /**
                 * 创建变体库存分配弹窗
                 */
                createVariantInventoryModal(colorVariants, productNumber) {
                    // 移除已存在的弹窗
                    const existingModal = document.getElementById('variant-inventory-modal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // 创建弹窗HTML
                    const modalHtml = `
                        <div id="variant-inventory-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-md w-full mx-4">
                                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                                        变体库存分配
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                        产品编号: ${productNumber}
                                    </p>
                                </div>
                                <div class="px-6 py-4 max-h-96 overflow-y-auto">
                                    <div class="space-y-4">
                                        ${colorVariants.map((variant, index) => `
                                            <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded">
                                                <div class="flex-1">
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        ${variant.colorName}
                                                    </label>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                        当前库存: ${variant.currentInventory}
                                                    </p>
                                                </div>
                                                <div class="flex-shrink-0 ml-4">
                                                    <input
                                                        type="number"
                                                        id="ratio-${variant.id}"
                                                        class="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                                                        value="${variant.ratio}"
                                                        min="0"
                                                        step="0.1"
                                                        placeholder="比例"
                                                    />
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                    <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded">
                                        <p class="text-xs text-gray-600 dark:text-gray-400">
                                            <strong>说明:</strong> 输入每个颜色的比例值，系统将根据 API返回的库存 × BoxQuantity × 比例值 来分配各变体的库存。
                                        </p>
                                    </div>
                                </div>
                                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                                    <button
                                        type="button"
                                        id="cancel-variant-sync"
                                        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                                    >
                                        取消
                                    </button>
                                    <button
                                        type="button"
                                        id="confirm-variant-sync"
                                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        确定同步
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                    // 添加弹窗到页面
                    document.body.insertAdjacentHTML('beforeend', modalHtml);

                    // 绑定事件
                    this.bindVariantModalEvents(colorVariants, productNumber);
                },

                /**
                 * 绑定变体弹窗事件
                 */
                bindVariantModalEvents(colorVariants, productNumber) {
                    const modal = document.getElementById('variant-inventory-modal');
                    const cancelBtn = document.getElementById('cancel-variant-sync');
                    const confirmBtn = document.getElementById('confirm-variant-sync');

                    // 取消按钮
                    cancelBtn.addEventListener('click', () => {
                        modal.remove();
                    });

                    // 点击背景关闭
                    modal.addEventListener('click', (e) => {
                        if (e.target === modal) {
                            modal.remove();
                        }
                    });

                    // 确定按钮
                    confirmBtn.addEventListener('click', () => {
                        this.processVariantInventorySync(colorVariants, productNumber, modal);
                    });
                },

                /**
                 * 处理变体库存同步
                 */
                processVariantInventorySync(colorVariants, productNumber, modal) {
                    try {
                        // 收集比例值
                        const ratios = {};
                        let hasValidRatio = false;

                        colorVariants.forEach(variant => {
                            const ratioInput = document.getElementById(`ratio-${variant.id}`);
                            const ratio = parseFloat(ratioInput.value) || 0;
                            ratios[variant.id] = ratio;
                            if (ratio > 0) {
                                hasValidRatio = true;
                            }
                        });

                        if (!hasValidRatio) {
                            this.$emitter.emit('add-flash', {
                                type: 'warning',
                                message: '请至少为一个变体设置大于0的比例值'
                            });
                            return;
                        }

                        console.log('变体比例设置:', ratios);

                        // 关闭弹窗
                        modal.remove();

                        // 显示加载状态
                        this.isLoading = true;

                        // 调用API获取库存数据
                        this.$axios.post("{{ route('admin.external_api.grid_data_search') }}", {
                                key: productNumber,
                                start: 0,
                                limit: 25
                            })
                            .then(response => {
                                console.log('API响应数据:', response.data);

                                if (response.data.success && response.data.data.topics && response.data.data.topics.length > 0) {
                                    const productData = response.data.data.topics[0];
                                    this.distributeVariantInventory(colorVariants, ratios, productData);
                                } else {
                                    this.$emitter.emit('add-flash', {
                                        type: 'error',
                                        message: '未获取到有效的产品数据'
                                    });
                                }

                                this.isLoading = false;
                            })
                            .catch(error => {
                                console.error('API请求失败:', error);
                                this.isLoading = false;

                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: '获取库存数据失败，请重试'
                                });
                            });

                    } catch (error) {
                        console.error('处理变体库存同步时出错:', error);
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '处理变体库存同步时出现错误'
                        });
                    }
                },

                /**
                 * 分配变体库存
                 */
                distributeVariantInventory(colorVariants, ratios, productData) {
                    try {
                        const baseInventory = parseInt(productData.Inventory1) || 0;
                        const boxQuantity = parseInt(productData.BoxQuantity) || 1;
                        const price = parseFloat(productData.S_Price1) || 0;

                        console.log('基础数据:', { baseInventory, boxQuantity, price });

                        let successCount = 0;
                        let totalAllocated = 0;

                        colorVariants.forEach(variant => {
                            const ratio = ratios[variant.id] || 0;
                            if (ratio > 0) {
                                const allocatedInventory = Math.floor(baseInventory * boxQuantity * ratio);
                                totalAllocated += allocatedInventory;

                                // 更新变体库存和价格
                                const success = this.updateVariantData(variant.id, {
                                    inventory: allocatedInventory,
                                    price: price
                                });

                                if (success) {
                                    successCount++;
                                    console.log(`变体 ${variant.colorName} 库存已更新为: ${allocatedInventory}, 价格: ${price}`);
                                }
                            }
                        });

                        // 显示成功消息
                        if (successCount > 0) {
                            this.$emitter.emit('add-flash', {
                                type: 'success',
                                message: `变体库存分配完成！\n成功更新 ${successCount} 个变体\n总分配库存: ${totalAllocated}\n单价: ${price}`
                            });
                        } else {
                            this.$emitter.emit('add-flash', {
                                type: 'warning',
                                message: '没有变体被更新，请检查设置'
                            });
                        }

                    } catch (error) {
                        console.error('分配变体库存时出错:', error);
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '分配变体库存时出现错误'
                        });
                    }
                },

                /**
                 * 更新变体数据
                 */
                updateVariantData(variantId, data) {
                    try {
                        // 更新库存字段
                        if (data.inventory !== undefined) {
                            // 查找库存输入框（可能有多个库存源）
                            const inventoryInputs = document.querySelectorAll(`input[name*="variants[${variantId}][inventory"]`);
                            inventoryInputs.forEach(input => {
                                input.value = data.inventory;
                                // 触发事件
                                const event = new Event('input', { bubbles: true });
                                input.dispatchEvent(event);
                            });

                            // 如果没找到具体的库存输入框，尝试通用方式
                            if (inventoryInputs.length === 0) {
                                const generalInventoryInput = document.querySelector(`input[name="variants[${variantId}][inventories][1]"]`);
                                if (generalInventoryInput) {
                                    generalInventoryInput.value = data.inventory;
                                    const event = new Event('input', { bubbles: true });
                                    generalInventoryInput.dispatchEvent(event);
                                }
                            }
                        }

                        // 更新价格字段
                        if (data.price !== undefined) {
                            const priceInput = document.querySelector(`input[name="variants[${variantId}][price]"]`);
                            if (priceInput) {
                                priceInput.value = data.price;
                                const event = new Event('input', { bubbles: true });
                                priceInput.dispatchEvent(event);
                            }
                        }

                        return true;

                    } catch (error) {
                        console.error(`更新变体 ${variantId} 数据时出错:`, error);
                        return false;
                    }
                }
            }
        });
    </script>
@endpushOnce

