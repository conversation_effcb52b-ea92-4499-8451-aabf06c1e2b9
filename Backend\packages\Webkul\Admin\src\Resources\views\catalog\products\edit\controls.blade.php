@switch($attribute->type)
    @case('text')
        <div class="flex flex-col gap-2.5">
            <div class="flex gap-2.5">
                <v-field
                    type="text"
                    name="{{ $attribute->code }}"
                    :rules="{{ $attribute->validations }}"
                    value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
                    v-slot="{ field }"
                    label="{{ $attribute->admin_name }}"
                >
                    <input
                        type="text"
                        id="{{ $attribute->code }}"
                        :class="[errors['{{ $attribute->code }}'] ? 'border border-red-600 hover:border-red-600' : '']"
                        class="w-full rounded-md border px-3 py-2.5 text-sm text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400"
                        name="{{ $attribute->code }}"
                        v-bind="field"
                        @if ($attribute->code == 'url_key') v-slugify @endif
                        @if ($attribute->code == 'name') v-slugify-target:url_key="setValues" @endif
                    >
                </v-field>
            </div>

            @if ($attribute->code == 'product_number')
                <!-- 使用独立的Vue组件 -->
                <v-sync-inventory></v-sync-inventory>
            @endif
        </div>

        @break
    @case('price')
        <x-admin::form.control-group.control
            type="price"
            :id="$attribute->code"
            :class="($attribute->code == 'price' ? 'py-2.5 bg-gray-50 text-xl font-bold' : '')"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        >
            <x-slot:currency :class="'dark:text-gray-300 ' . ($attribute->code == 'price' ? 'bg-gray-50 dark:bg-gray-900 text-xl' : '')">
                {{ core()->currencySymbol(core()->getBaseCurrencyCode()) }}
            </x-slot>
        </x-admin::form.control-group.control>

        @break
    @case('textarea')
        <x-admin::form.control-group.control
            type="textarea"
            :id="$attribute->code"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
            :tinymce="(bool) $attribute->enable_wysiwyg"
            :prompt="core()->getConfigData('general.magic_ai.content_generation.product_' . $attribute->code . '_prompt')"
        />

        @break
    @case('date')
        <x-admin::form.control-group.control
            type="date"
            :id="$attribute->code"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        />

        @break
    @case('datetime')
        <x-admin::form.control-group.control
            type="datetime"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        />

        @break
    @case('select')
        @if($attribute->code === 'brand' || $attribute->code === 'device' || $attribute->code === 'Device')
            @include('admin::catalog.products.edit.cascading-select-control', ['attribute' => $attribute, 'product' => $product])
        @else
            <x-admin::form.control-group.control
                type="select"
                :id="$attribute->code"
                :name="$attribute->code"
                ::rules="{{ $attribute->validations }}"
                :value="old($attribute->code) ?: $product[$attribute->code]"
                :label="$attribute->admin_name"
            >
                @php
                    $selectedOption = old($attribute->code) ?: $product[$attribute->code];

                    if ($attribute->code != 'tax_category_id') {
                        $options = $attribute->options()->orderBy('sort_order')->get();
                    } else {
                        $options = app('Webkul\Tax\Repositories\TaxCategoryRepository')->all();
                    }
                @endphp

                @foreach ($options as $option)
                    <option
                        value="{{ $option->id }}"
                        {{ $selectedOption == $option->id ? 'selected' : '' }}
                    >
                        {{ $option->admin_name ?? $option->name }}
                    </option>
                @endforeach
            </x-admin::form.control-group.control>
        @endif

        @break
    @case('multiselect')
        @php
            $selectedOption = old($attribute->code) ?: explode(',', $product[$attribute->code]);
        @endphp

        <x-admin::form.control-group.control
            type="multiselect"
            :id="$attribute->code . '[]'"
            :name="$attribute->code . '[]'"
            ::rules="{{ $attribute->validations }}"
            :label="$attribute->admin_name"
        >
            @foreach ($attribute->options()->orderBy('sort_order')->get() as $option)
                <option
                    value="{{ $option->id }}"
                    {{ in_array($option->id, $selectedOption) ? 'selected' : ''}}
                >
                    {{ $option->admin_name }}
                </option>
            @endforeach
        </x-admin::form.control-group.control>

        @break
    @case('checkbox')
        @php
            $selectedOption = old($attribute->code) ?: explode(',', $product[$attribute->code]);
        @endphp

        @foreach ($attribute->options as $option)
            <div class="mb-2 flex items-center gap-2.5 last:!mb-0">
                <x-admin::form.control-group.control
                    type="checkbox"
                    :id="$attribute->code . '_' . $option->id"
                    :name="$attribute->code . '[]'"
                    ::rules="{{ $attribute->validations }}"
                    :value="$option->id"
                    :for="$attribute->code . '_' . $option->id"
                    :label="$attribute->admin_name"
                    :checked="in_array($option->id, $selectedOption)"
                />

                <label
                    class="cursor-pointer select-none text-xs font-medium text-gray-600 dark:text-gray-300"
                    for="{{ $attribute->code . '_' . $option->id }}"
                >
                    {{ $option->admin_name }}
                </label>
            </div>
        @endforeach

        @break
    @case('boolean')
        @php $selectedValue = old($attribute->code) ?: $product[$attribute->code] @endphp

        <x-admin::form.control-group.control
            type="switch"
            :id="$attribute->code"
            :name="$attribute->code"
            :value="1"
            :label="$attribute->admin_name"
            :checked="(boolean) $selectedValue"
        />

        @break
    @case('image')
    @case('file')
        <div class="flex gap-2.5">
            @if ($product[$attribute->code])
                <a
                    href="{{ route('admin.catalog.products.file.download', [$product->id, $attribute->id] )}}"
                    class="flex"
                >
                    @if ($attribute->type == 'image')
                        @if (Storage::exists($product[$attribute->code]))
                            <img
                                src="{{ Storage::url($product[$attribute->code]) }}"
                                class="h-[45px] w-[45px] overflow-hidden rounded border hover:border-gray-400 dark:border-gray-800"
                            />
                        @endif
                    @else
                        <div class="inline-flex w-full max-w-max cursor-pointer appearance-none items-center justify-between gap-x-1 rounded-md border border-transparent p-1.5 text-center text-gray-600 transition-all marker:shadow hover:bg-gray-200 active:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-800">
                            <i class="icon-down-stat text-2xl"></i>
                        </div>
                    @endif
                </a>

                <input
                    type="hidden"
                    name="{{ $attribute->code }}"
                    value="{{ $product[$attribute->code] }}"
                />
            @endif

            <v-field
                type="file"
                class="w-full"
                name="{{ $attribute->code }}"
                :rules="{{ $attribute->validations }}"
                v-slot="{ handleChange, handleBlur }"
                label="{{ $attribute->admin_name }}"
            >
                <input
                    type="file"
                    id="{{ $attribute->code }}"
                    :class="[errors['{{ $attribute->code }}'] ? 'border border-red-600 hover:border-red-600' : '']"
                    class="w-full rounded-md border px-3 py-2.5 text-sm text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:text-gray-300 dark:file:bg-gray-800 dark:file:dark:text-white dark:hover:border-gray-400 dark:focus:border-gray-400"
                    name="{{ $attribute->code }}"
                    @change="handleChange"
                    @blur="handleBlur"
                >
            </v-field>
        </div>

        @if ($product[$attribute->code])
            <div class="mt-2.5 flex items-center gap-2.5">
                <x-admin::form.control-group.control
                    type="checkbox"
                    :id="$attribute->code . '_delete'"
                    :name="$attribute->code . '[delete]'"
                    value="1"
                    :for="$attribute->code . '_delete'"
                />

                <label
                    for="{{ $attribute->code . '_delete' }}"
                    class="cursor-pointer select-none text-sm text-gray-600 dark:text-gray-300"
                >
                    @lang('admin::app.catalog.products.edit.remove')
                </label>
            </div>
        @endif

        @break
    @case('multiimage')
        {{-- multiimage type is handled separately in the images section --}}
        @break
@endswitch

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-sync-inventory-template"
    >
        <div class="flex">
            <button
                type="button"
                class="secondary-button text-sm py-1.5 px-2.5"
                @click="syncInventory"
                :disabled="isLoading"
            >
                @{{ isLoading ? '同步中...' : '@lang('admin::app.catalog.products.edit.sync-inventory')' }}
            </button>
        </div>
    </script>

    <script type="module">
        app.component('v-sync-inventory', {
            template: '#v-sync-inventory-template',

            data() {
                return {
                    isLoading: false,
                }
            },

            methods: {
                /**
                 * 同步库存方法
                 */
                syncInventory() {
                    // 获取Product Number输入框的值
                    const productNumberInput = document.getElementById('product_number');

                    if (!productNumberInput) {
                        console.error('未找到Product Number输入框');
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '未找到Product Number输入框'
                        });
                        return;
                    }

                    const productNumber = productNumberInput.value.trim();

                    if (!productNumber) {
                        console.warn('Product Number为空，无法进行同步');
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: 'Product Number为空，请先填写产品编号'
                        });
                        return;
                    }

                    console.log('开始同步库存，Product Number:', productNumber);

                    this.isLoading = true;

                    // 使用Bagisto的axios实例发起请求
                    this.$axios.post("{{ route('admin.external_api.grid_data_search') }}", {
                            key: productNumber,
                            start: 0,
                            limit: 25
                        })
                        .then(response => {
                            console.log('同步库存响应数据:', response.data);

                            if (response.data.success) {
                                console.log('库存同步成功:', response.data.data);

                                // 解析返回的数据
                                const data = response.data.data;
                                const totalCount = data.totalCount || 0;
                                const topics = data.topics || [];

                                if (topics.length > 0) {
                                    const product = topics[0];

                                    // 构建成功消息
                                    let successMessage = `库存同步成功！\n`;
                                    successMessage += `产品名称: ${product.NameCn || product.NameIt || '未知'}\n`;
                                    successMessage += `产品编码: ${product.Code || '未知'}\n`;
                                    successMessage += `库存数量: ${product.Inventory1 || 0}\n`;
                                    successMessage += `销售价格: ${product.S_Price1 || '0.00'}`;

                                    // 显示成功弹窗
                                    this.$emitter.emit('add-flash', {
                                        type: 'success',
                                        message: successMessage
                                    });

                                    // 自动填充表单字段
                                    this.fillFormFields(product);

                                } else {
                                    this.$emitter.emit('add-flash', {
                                        type: 'warning',
                                        message: '同步成功，但未找到匹配的产品数据'
                                    });
                                }

                            } else {
                                console.error('库存同步失败:', response.data.message || response.data.error);

                                this.$emitter.emit('add-flash', {
                                    type: 'error',
                                    message: `库存同步失败: ${response.data.message || response.data.error || '未知错误'}`
                                });
                            }

                            this.isLoading = false;
                        })
                        .catch(error => {
                            console.error('同步库存请求失败:', error);
                            this.isLoading = false;

                            // 处理不同类型的错误
                            let errorMessage = '库存同步失败，请重试';

                            if (error.response) {
                                // 服务器响应了错误状态码
                                const status = error.response.status;
                                const data = error.response.data;

                                switch (status) {
                                    case 404:
                                        errorMessage = '接口不存在，请检查配置';
                                        break;
                                    case 422:
                                        errorMessage = `参数验证失败: ${data.message || '请检查输入参数'}`;
                                        break;
                                    case 500:
                                        errorMessage = '服务器内部错误，请稍后重试';
                                        break;
                                    default:
                                        errorMessage = data.message || `请求失败 (${status})`;
                                }
                            } else if (error.request) {
                                // 请求已发送但没有收到响应
                                errorMessage = '网络连接失败，请检查网络连接';
                            }

                            this.$emitter.emit('add-flash', {
                                type: 'error',
                                message: errorMessage
                            });
                        });
                },

                /**
                 * 填充表单字段
                 */
                fillFormFields(productData) {
                    try {
                        // 映射字段关系
                        const fieldMappings = {
                            'sku': productData.id_no,           // id_no -> sku
                            'group_code': productData.CpflCode, // CpflCode -> group_code
                            'name': productData.NameIt          // NameIt -> name
                        };

                        // 填充字段
                        Object.keys(fieldMappings).forEach(fieldId => {
                            const value = fieldMappings[fieldId];
                            if (value) {
                                this.setFieldValue(fieldId, value);
                            }
                        });

                        console.log('表单字段填充完成');

                    } catch (error) {
                        console.error('填充表单字段时出错:', error);
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: '数据同步成功，但自动填充表单时出现问题'
                        });
                    }
                },

                /**
                 * 设置字段值
                 */
                setFieldValue(fieldId, value) {
                    try {
                        // 方法1: 通过ID直接设置
                        const element = document.getElementById(fieldId);
                        if (element) {
                            // 设置值
                            element.value = value;

                            // 触发input事件，确保Vue能检测到变化
                            const inputEvent = new Event('input', { bubbles: true });
                            element.dispatchEvent(inputEvent);

                            // 触发change事件
                            const changeEvent = new Event('change', { bubbles: true });
                            element.dispatchEvent(changeEvent);

                            console.log(`字段 ${fieldId} 已设置为: ${value}`);
                            return true;
                        }

                        // 方法2: 通过name属性查找
                        const elementByName = document.querySelector(`input[name="${fieldId}"]`);
                        if (elementByName) {
                            elementByName.value = value;

                            // 触发事件
                            const inputEvent = new Event('input', { bubbles: true });
                            elementByName.dispatchEvent(inputEvent);

                            const changeEvent = new Event('change', { bubbles: true });
                            elementByName.dispatchEvent(changeEvent);

                            console.log(`字段 ${fieldId} (通过name) 已设置为: ${value}`);
                            return true;
                        }

                        console.warn(`未找到字段: ${fieldId}`);
                        return false;

                    } catch (error) {
                        console.error(`设置字段 ${fieldId} 时出错:`, error);
                        return false;
                    }
                }
            }
        });
    </script>
@endpushOnce

