@switch($attribute->type)
    @case('text')
        <div class="flex flex-col gap-2.5">
            <div class="flex gap-2.5">
                <v-field
                    type="text"
                    name="{{ $attribute->code }}"
                    :rules="{{ $attribute->validations }}"
                    value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
                    v-slot="{ field }"
                    label="{{ $attribute->admin_name }}"
                >
                    <input
                        type="text"
                        id="{{ $attribute->code }}"
                        :class="[errors['{{ $attribute->code }}'] ? 'border border-red-600 hover:border-red-600' : '']"
                        class="w-full rounded-md border px-3 py-2.5 text-sm text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400"
                        name="{{ $attribute->code }}"
                        v-bind="field"
                        @if ($attribute->code == 'url_key') v-slugify @endif
                        @if ($attribute->code == 'name') v-slugify-target:url_key="setValues" @endif
                    >
                </v-field>
            </div>

            @if ($attribute->code == 'product_number')
                <!-- 使用独立的Vue组件 -->
                <v-sync-inventory></v-sync-inventory>
            @endif
        </div>

        @break
    @case('price')
        <x-admin::form.control-group.control
            type="price"
            :id="$attribute->code"
            :class="($attribute->code == 'price' ? 'py-2.5 bg-gray-50 text-xl font-bold' : '')"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        >
            <x-slot:currency :class="'dark:text-gray-300 ' . ($attribute->code == 'price' ? 'bg-gray-50 dark:bg-gray-900 text-xl' : '')">
                {{ core()->currencySymbol(core()->getBaseCurrencyCode()) }}
            </x-slot>
        </x-admin::form.control-group.control>

        @break
    @case('textarea')
        <x-admin::form.control-group.control
            type="textarea"
            :id="$attribute->code"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
            :tinymce="(bool) $attribute->enable_wysiwyg"
            :prompt="core()->getConfigData('general.magic_ai.content_generation.product_' . $attribute->code . '_prompt')"
        />

        @break
    @case('date')
        <x-admin::form.control-group.control
            type="date"
            :id="$attribute->code"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        />

        @break
    @case('datetime')
        <x-admin::form.control-group.control
            type="datetime"
            :name="$attribute->code"
            ::rules="{{ $attribute->validations }}"
            value="{{ old($attribute->code) ?: $product[$attribute->code] }}"
            :label="$attribute->admin_name"
        />

        @break
    @case('select')
        @if($attribute->code === 'brand' || $attribute->code === 'device' || $attribute->code === 'Device')
            @include('admin::catalog.products.edit.cascading-select-control', ['attribute' => $attribute, 'product' => $product])
        @else
            <x-admin::form.control-group.control
                type="select"
                :id="$attribute->code"
                :name="$attribute->code"
                ::rules="{{ $attribute->validations }}"
                :value="old($attribute->code) ?: $product[$attribute->code]"
                :label="$attribute->admin_name"
            >
                @php
                    $selectedOption = old($attribute->code) ?: $product[$attribute->code];

                    if ($attribute->code != 'tax_category_id') {
                        $options = $attribute->options()->orderBy('sort_order')->get();
                    } else {
                        $options = app('Webkul\Tax\Repositories\TaxCategoryRepository')->all();
                    }
                @endphp

                @foreach ($options as $option)
                    <option
                        value="{{ $option->id }}"
                        {{ $selectedOption == $option->id ? 'selected' : '' }}
                    >
                        {{ $option->admin_name ?? $option->name }}
                    </option>
                @endforeach
            </x-admin::form.control-group.control>
        @endif

        @break
    @case('multiselect')
        @php
            $selectedOption = old($attribute->code) ?: explode(',', $product[$attribute->code]);
        @endphp

        <x-admin::form.control-group.control
            type="multiselect"
            :id="$attribute->code . '[]'"
            :name="$attribute->code . '[]'"
            ::rules="{{ $attribute->validations }}"
            :label="$attribute->admin_name"
        >
            @foreach ($attribute->options()->orderBy('sort_order')->get() as $option)
                <option
                    value="{{ $option->id }}"
                    {{ in_array($option->id, $selectedOption) ? 'selected' : ''}}
                >
                    {{ $option->admin_name }}
                </option>
            @endforeach
        </x-admin::form.control-group.control>

        @break
    @case('checkbox')
        @php
            $selectedOption = old($attribute->code) ?: explode(',', $product[$attribute->code]);
        @endphp

        @foreach ($attribute->options as $option)
            <div class="mb-2 flex items-center gap-2.5 last:!mb-0">
                <x-admin::form.control-group.control
                    type="checkbox"
                    :id="$attribute->code . '_' . $option->id"
                    :name="$attribute->code . '[]'"
                    ::rules="{{ $attribute->validations }}"
                    :value="$option->id"
                    :for="$attribute->code . '_' . $option->id"
                    :label="$attribute->admin_name"
                    :checked="in_array($option->id, $selectedOption)"
                />

                <label
                    class="cursor-pointer select-none text-xs font-medium text-gray-600 dark:text-gray-300"
                    for="{{ $attribute->code . '_' . $option->id }}"
                >
                    {{ $option->admin_name }}
                </label>
            </div>
        @endforeach

        @break
    @case('boolean')
        @php $selectedValue = old($attribute->code) ?: $product[$attribute->code] @endphp

        <x-admin::form.control-group.control
            type="switch"
            :id="$attribute->code"
            :name="$attribute->code"
            :value="1"
            :label="$attribute->admin_name"
            :checked="(boolean) $selectedValue"
        />

        @break
    @case('image')
    @case('file')
        <div class="flex gap-2.5">
            @if ($product[$attribute->code])
                <a
                    href="{{ route('admin.catalog.products.file.download', [$product->id, $attribute->id] )}}"
                    class="flex"
                >
                    @if ($attribute->type == 'image')
                        @if (Storage::exists($product[$attribute->code]))
                            <img
                                src="{{ Storage::url($product[$attribute->code]) }}"
                                class="h-[45px] w-[45px] overflow-hidden rounded border hover:border-gray-400 dark:border-gray-800"
                            />
                        @endif
                    @else
                        <div class="inline-flex w-full max-w-max cursor-pointer appearance-none items-center justify-between gap-x-1 rounded-md border border-transparent p-1.5 text-center text-gray-600 transition-all marker:shadow hover:bg-gray-200 active:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-800">
                            <i class="icon-down-stat text-2xl"></i>
                        </div>
                    @endif
                </a>

                <input
                    type="hidden"
                    name="{{ $attribute->code }}"
                    value="{{ $product[$attribute->code] }}"
                />
            @endif

            <v-field
                type="file"
                class="w-full"
                name="{{ $attribute->code }}"
                :rules="{{ $attribute->validations }}"
                v-slot="{ handleChange, handleBlur }"
                label="{{ $attribute->admin_name }}"
            >
                <input
                    type="file"
                    id="{{ $attribute->code }}"
                    :class="[errors['{{ $attribute->code }}'] ? 'border border-red-600 hover:border-red-600' : '']"
                    class="w-full rounded-md border px-3 py-2.5 text-sm text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:text-gray-300 dark:file:bg-gray-800 dark:file:dark:text-white dark:hover:border-gray-400 dark:focus:border-gray-400"
                    name="{{ $attribute->code }}"
                    @change="handleChange"
                    @blur="handleBlur"
                >
            </v-field>
        </div>

        @if ($product[$attribute->code])
            <div class="mt-2.5 flex items-center gap-2.5">
                <x-admin::form.control-group.control
                    type="checkbox"
                    :id="$attribute->code . '_delete'"
                    :name="$attribute->code . '[delete]'"
                    value="1"
                    :for="$attribute->code . '_delete'"
                />

                <label
                    for="{{ $attribute->code . '_delete' }}"
                    class="cursor-pointer select-none text-sm text-gray-600 dark:text-gray-300"
                >
                    @lang('admin::app.catalog.products.edit.remove')
                </label>
            </div>
        @endif

        @break
    @case('multiimage')
        {{-- multiimage type is handled separately in the images section --}}
        @break
@endswitch

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-sync-inventory-template"
    >
        <div class="flex">
            <button
                type="button"
                class="secondary-button text-sm py-1.5 px-2.5"
                @click="syncInventory"
                :disabled="isLoading"
            >
                @{{ isLoading ? '同步中...' : '@lang('admin::app.catalog.products.edit.sync-inventory')' }}
            </button>

            <!-- 变体库存分配弹窗 -->
            <x-admin::modal ref="variantInventoryModal">
                <!-- Modal Header -->
                <x-slot:header>
                    <p class="text-lg font-bold text-gray-800 dark:text-white">
                        变体库存分配
                    </p>
                </x-slot>

                <!-- Modal Content -->
                <x-slot:content>
                    <div class="grid gap-4">
                        <p class="text-sm text-gray-600 dark:text-gray-300">
                            请设置各颜色变体的库存分配比例（总和应为@{{ packQuantity }}）：
                        </p>
                        
                        <div class="space-y-3">
                            <div 
                                v-for="variant in availableVariants" 
                                :key="variant.id"
                                class="flex items-center justify-between"
                            >
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    @{{ variant.name || 'Variant ' + variant.id }}
                                    <span class="text-xs text-gray-500">(@{{ getColorName(variant.color) }})</span>
                                </label>
                                <div class="flex items-center">
                                    <input
                                        type="number"
                                        v-model.number="variant.ratio"
                                        class="w-20 rounded border px-2 py-1 text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
                                        min="0"
                                        :max="packQuantity"
                                        step="1"
                                        :placeholder="'最大' + packQuantity"
                                    />
                                    <span class="ml-1 text-sm text-gray-500">包</span>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 text-right">
                            <span class="text-sm" :class="isValidTotal ? 'text-green-600' : 'text-red-600'">
                                当前总计: @{{ totalRatio }} / @{{ packQuantity }}
                                <span v-if="inventoryData && inventoryData.PackQuantity">
                                    (需要等于PackQuantity: @{{ packQuantity }})
                                </span>
                            </span>
                        </div>
                    </div>
                </x-slot>

                <!-- Modal Footer -->
                <x-slot:footer>
                    <button
                        type="button"
                        class="secondary-button"
                        @click="closeVariantModal"
                    >
                        取消
                    </button>
                    
                    <button
                        type="button"
                        class="primary-button"
                        @click="distributeVariantInventory"
                        :disabled="!isValidTotal || isLoading"
                    >
                        @{{ isLoading ? '处理中...' : '确定分配' }}
                    </button>
                </x-slot:footer>
            </x-admin::modal>
        </div>
    </script>

    <script type="module">
        app.component('v-sync-inventory', {
            template: '#v-sync-inventory-template',

            data() {
                return {
                    isLoading: false,
                    availableVariants: [],
                    inventoryData: null,
                    colorOptions: {
                        1: 'Red',
                        2: 'Green', 
                        3: 'Blue',
                        4: 'Black',
                        5: 'White',
                        6: 'Yellow',
                        7: 'Purple',
                        8: 'Orange',
                        9: 'Pink',
                        10: 'Gray'
                    }
                }
            },

            computed: {
                totalRatio() {
                    return this.availableVariants.reduce((sum, variant) => sum + (parseInt(variant.ratio) || 0), 0);
                },

                packQuantity() {
                    return parseInt(this.inventoryData?.PackQuantity) || 1;
                },

                isValidTotal() {
                    return this.totalRatio === this.packQuantity;
                },

                isConfigurableProduct() {
                    return '{{ $product->type }}' === 'configurable';
                },

                variants() {
                    // 从全局组件中获取变体数据
                    return window.configurableVariants || [];
                }
            },

            methods: {
                /**
                 * 同步库存方法
                 */
                syncInventory() {
                    // 获取Product Number输入框的值
                    const productNumberInput = document.getElementById('product_number');

                    if (!productNumberInput) {
                        console.error('未找到Product Number输入框');
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '未找到Product Number输入框'
                        });
                        return;
                    }

                    const productNumber = productNumberInput.value.trim();

                    if (!productNumber) {
                        console.warn('Product Number为空，无法进行同步');
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: 'Product Number为空，请先填写产品编号'
                        });
                        return;
                    }

                    console.log('开始同步库存，Product Number:', productNumber, '产品类型:', '{{ $product->type }}');

                    // 检查是否为configurable产品
                    if (this.isConfigurableProduct) {
                        this.syncConfigurableInventory(productNumber);
                    } else {
                        this.syncSimpleInventory(productNumber);
                    }
                },

                /**
                 * 通用库存数据获取方法
                 */
                async fetchInventoryDataFromAPI(productNumber) {
                    try {
                        const response = await this.$axios.post("{{ route('admin.external_api.grid_data_search') }}", {
                            key: productNumber,
                            start: 0,
                            limit: 25
                        });

                        console.log('同步库存响应数据:', response.data);

                        if (response.data.success && response.data.data.topics.length > 0) {
                            const product = response.data.data.topics[0];
                            console.log('库存同步成功:', product);
                            return { success: true, product };
                        } else {
                            return { 
                                success: false, 
                                error: response.data.message || response.data.error || '未找到匹配的产品数据' 
                            };
                        }
                    } catch (error) {
                        console.error('同步库存请求失败:', error);
                        return { success: false, error: error };
                    }
                },

                /**
                 * 同步简单产品库存
                 */
                async syncSimpleInventory(productNumber) {
                    this.isLoading = true;

                    const result = await this.fetchInventoryDataFromAPI(productNumber);

                    if (result.success) {
                        const product = result.product;

                        // 构建成功消息
                        let successMessage = `库存同步成功！\n`;
                        successMessage += `产品名称: ${product.NameCn || product.NameIt || '未知'}\n`;
                        successMessage += `产品编码: ${product.Code || '未知'}\n`;
                        successMessage += `库存数量: ${product.Inventory1 || 0}\n`;
                        successMessage += `销售价格: ${product.S_Price1 || '0.00'}`;

                        // 显示成功弹窗
                        this.$emitter.emit('add-flash', {
                            type: 'success',
                            message: successMessage
                        });

                        // 自动填充表单字段
                        this.fillFormFields(product);
                    } else {
                        if (result.error && typeof result.error === 'object') {
                            this.handleSyncError(result.error);
                        } else {
                            this.$emitter.emit('add-flash', {
                                type: 'warning',
                                message: result.error || '同步失败'
                            });
                        }
                    }

                    this.isLoading = false;
                },

                /**
                 * 同步configurable产品库存
                 */
                async syncConfigurableInventory(productNumber) {
                    // 获取变体数据
                    this.getVariantsFromPage();
                    
                    if (this.availableVariants.length === 0) {
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: '未找到产品变体，请先创建变体'
                        });
                        return;
                    }

                    // 显示变体分配弹窗
                    this.$refs.variantInventoryModal.open();

                    // 获取库存数据
                    this.isLoading = true;
                    const result = await this.fetchInventoryDataFromAPI(productNumber);

                    if (result.success) {
                        this.inventoryData = result.product;
                        console.log('获取到库存数据:', this.inventoryData);
                        
                        // 自动填充主产品表单字段
                        this.fillFormFields(result.product);
                    } else {
                        if (result.error && typeof result.error === 'object') {
                            this.handleSyncError(result.error);
                        } else {
                            this.$emitter.emit('add-flash', {
                                type: 'warning',
                                message: result.error || '未找到匹配的库存数据'
                            });
                        }
                        this.$refs.variantInventoryModal.close();
                    }
                    
                    this.isLoading = false;
                },

                /**
                 * 获取页面中的变体数据
                 */
                getVariantsFromPage() {
                    try {
                        let variants = [];
                        
                        // 优先从全局window对象获取最新的变体数据
                        if (window.configurableVariants && window.configurableVariants.length > 0) {
                            variants = window.configurableVariants;
                            console.log('从全局变量获取变体数据:', variants);
                        } else {
                            // 如果全局变量没有，尝试从Vue组件获取
                            const getAllComponents = (component) => {
                                let components = [component];
                                if (component.$children) {
                                    component.$children.forEach(child => {
                                        components = components.concat(getAllComponents(child));
                                    });
                                }
                                return components;
                            };

                            const allComponents = getAllComponents(this.$root);
                            const variationsComponent = allComponents.find(child => 
                                child.$options.name === 'v-product-variations' || 
                                (child.variants && Array.isArray(child.variants))
                            );

                            if (variationsComponent && variationsComponent.variants) {
                                variants = variationsComponent.variants;
                                console.log('从Vue组件获取变体数据:', variants);
                            }
                        }

                        if (variants.length === 0) {
                            console.warn('未找到变体数据');
                            this.availableVariants = [];
                            return;
                        }

                        // 转换变体数据并设置默认比例
                        this.availableVariants = variants.map(variant => ({
                            id: variant.id,
                            name: variant.name || `Variant ${variant.id}`,
                            sku: variant.sku,
                            color: variant.color,
                            ratio: Math.round(100 / variants.length * 10) / 10 // 默认平均分配
                        }));

                        console.log('处理后的变体数据:', this.availableVariants);

                    } catch (error) {
                        console.error('获取变体数据失败:', error);
                        this.availableVariants = [];
                    }
                },



                /**
                 * 分配变体库存
                 */
                distributeVariantInventory() {
                    if (!this.inventoryData) {
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '库存数据不可用'
                        });
                        return;
                    }

                    // 获取API返回的数据
                    const inventory1 = parseInt(this.inventoryData.Inventory1) || 0;
                    const boxQuantity = parseInt(this.inventoryData.BoxQuantity) || 1;
                    const packQuantity = parseInt(this.inventoryData.PackQuantity) || 1;
                    const priceValue = this.inventoryData.S_Price1 || '0.00';

                    // 计算用户输入值的总和
                    const totalInputValue = this.availableVariants.reduce((sum, variant) => {
                        return sum + (parseInt(variant.ratio) || 0);
                    }, 0);

                    // 验证总和是否等于PackQuantity
                    if (totalInputValue !== packQuantity) {
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: `输入值总和(${totalInputValue})必须等于PackQuantity(${packQuantity})`
                        });
                        return;
                    }

                    this.isLoading = true;

                    try {
                        console.log('库存计算参数:', {
                            inventory1,
                            boxQuantity,
                            packQuantity,
                            totalInputValue,
                            priceValue
                        });

                        // 分配库存到各个变体
                        this.availableVariants.forEach(variant => {
                            const inputValue = parseInt(variant.ratio) || 0;
                            // 计算百分比：用户输入值 / PackQuantity
                            const percentage = inputValue / packQuantity;
                            // 计算库存：Inventory1 × BoxQuantity × PackQuantity × 百分比
                            const allocatedInventory = Math.floor(inventory1 * boxQuantity * packQuantity * percentage);

                            console.log(`变体 ${variant.colorName}:`, {
                                inputValue,
                                percentage: (percentage * 100).toFixed(2) + '%',
                                allocatedInventory
                            });

                            this.updateVariantInventoryAndPrice(variant.id, allocatedInventory, priceValue);
                        });

                        const totalCalculatedInventory = inventory1 * boxQuantity * packQuantity;
                        this.$emitter.emit('add-flash', {
                            type: 'success',
                            message: `变体库存分配完成！\n总库存: ${totalCalculatedInventory}\n价格: ${priceValue}\nPackQuantity: ${packQuantity}`
                        });

                        this.$refs.variantInventoryModal.close();

                    } catch (error) {
                        console.error('分配变体库存失败:', error);
                        this.$emitter.emit('add-flash', {
                            type: 'error',
                            message: '变体库存分配失败: ' + error.message
                        });
                    }

                    this.isLoading = false;
                },

                /**
                 * 更新变体库存和价格
                 */
                updateVariantInventoryAndPrice(variantId, inventory, price) {
                    try {
                        // 查找对应的变体组件
                        const result = this.findVariantComponent(variantId);
                        
                        if (result && result.variant) {
                            const variant = result.variant;

                            // 更新库存
                            if (variant.inventories) {
                                if (typeof variant.inventories === 'object') {
                                    // 如果是对象格式（新变体）
                                    const firstSourceId = Object.keys(variant.inventories)[0];
                                    if (firstSourceId) {
                                        variant.inventories[firstSourceId] = inventory;
                                    }
                                } else if (Array.isArray(variant.inventories) && variant.inventories.length > 0) {
                                    // 如果是数组格式（已保存的变体）
                                    variant.inventories[0].qty = inventory;
                                }
                            } else {
                                // 如果没有库存对象，创建一个
                                variant.inventories = { 1: inventory }; // 假设库存源ID为1
                            }

                            // 更新价格
                            variant.price = price;

                            // 如果是通过Vue组件找到的，触发响应式更新
                            if (result.component && result.component.$forceUpdate) {
                                result.component.$forceUpdate();
                            }

                            console.log(`变体 ${variantId} 库存更新为: ${inventory}, 价格更新为: ${price}`);
                            console.log('更新后的变体数据:', variant);
                        } else {
                            console.warn(`未找到变体组件: ${variantId}`);
                        }

                    } catch (error) {
                        console.error(`更新变体 ${variantId} 失败:`, error);
                    }
                },

                /**
                 * 查找变体组件
                 */
                findVariantComponent(variantId) {
                    try {
                        // 方法1: 优先从全局变量查找和更新
                        if (window.configurableVariants) {
                            const variant = window.configurableVariants.find(v => v.id == variantId);
                            if (variant) {
                                return { variant, isGlobal: true };
                            }
                        }

                        // 方法2: 从Vue组件查找
                        const getAllComponents = (component) => {
                            let components = [component];
                            if (component.$children) {
                                component.$children.forEach(child => {
                                    components = components.concat(getAllComponents(child));
                                });
                            }
                            return components;
                        };

                        const allComponents = getAllComponents(this.$root);
                        const variationsComponent = allComponents.find(child => 
                            child.$options.name === 'v-product-variations' || 
                            (child.variants && Array.isArray(child.variants))
                        );

                        if (variationsComponent && variationsComponent.variants) {
                            const variant = variationsComponent.variants.find(v => v.id == variantId);
                            if (variant) {
                                return { variant, component: variationsComponent };
                            }
                        }

                        console.warn(`未找到变体: ${variantId}`);
                        return null;
                    } catch (error) {
                        console.error('查找变体组件失败:', error);
                        return null;
                    }
                },

                /**
                 * 获取颜色名称
                 */
                getColorName(colorId) {
                    return this.colorOptions[colorId] || `Color ${colorId}`;
                },

                /**
                 * 关闭变体弹窗
                 */
                closeVariantModal() {
                    this.$refs.variantInventoryModal.close();
                    this.availableVariants = [];
                    this.inventoryData = null;
                    this.isLoading = false;
                },

                /**
                 * 处理同步错误
                 */
                handleSyncError(error) {
                    console.error('同步库存请求失败:', error);
                            this.isLoading = false;

                            // 处理不同类型的错误
                            let errorMessage = '库存同步失败，请重试';

                            if (error.response) {
                                // 服务器响应了错误状态码
                                const status = error.response.status;
                                const data = error.response.data;

                                switch (status) {
                                    case 404:
                                        errorMessage = '接口不存在，请检查配置';
                                        break;
                                    case 422:
                                        errorMessage = `参数验证失败: ${data.message || '请检查输入参数'}`;
                                        break;
                                    case 500:
                                        errorMessage = '服务器内部错误，请稍后重试';
                                        break;
                                    default:
                                        errorMessage = data.message || `请求失败 (${status})`;
                                }
                            } else if (error.request) {
                                // 请求已发送但没有收到响应
                                errorMessage = '网络连接失败，请检查网络连接';
                            }

                            this.$emitter.emit('add-flash', {
                                type: 'error',
                                message: errorMessage
                        });
                },

                /**
                 * 填充表单字段
                 */
                fillFormFields(productData) {
                    try {
                        // 映射字段关系
                        const fieldMappings = {
                            'sku': productData.id_no,           // id_no -> sku
                            'group_code': productData.CpflCode, // CpflCode -> group_code
                            'name': productData.NameIt          // NameIt -> name
                        };

                        // 填充字段
                        Object.keys(fieldMappings).forEach(fieldId => {
                            const value = fieldMappings[fieldId];
                            if (value) {
                                this.setFieldValue(fieldId, value);
                            }
                        });

                        console.log('表单字段填充完成');

                    } catch (error) {
                        console.error('填充表单字段时出错:', error);
                        this.$emitter.emit('add-flash', {
                            type: 'warning',
                            message: '数据同步成功，但自动填充表单时出现问题'
                        });
                    }
                },

                /**
                 * 设置字段值
                 */
                setFieldValue(fieldId, value) {
                    try {
                        // 方法1: 通过ID直接设置
                        const element = document.getElementById(fieldId);
                        if (element) {
                            // 设置值
                            element.value = value;

                            // 触发input事件，确保Vue能检测到变化
                            const inputEvent = new Event('input', { bubbles: true });
                            element.dispatchEvent(inputEvent);

                            // 触发change事件
                            const changeEvent = new Event('change', { bubbles: true });
                            element.dispatchEvent(changeEvent);

                            console.log(`字段 ${fieldId} 已设置为: ${value}`);
                            return true;
                        }

                        // 方法2: 通过name属性查找
                        const elementByName = document.querySelector(`input[name="${fieldId}"]`);
                        if (elementByName) {
                            elementByName.value = value;

                            // 触发事件
                            const inputEvent = new Event('input', { bubbles: true });
                            elementByName.dispatchEvent(inputEvent);

                            const changeEvent = new Event('change', { bubbles: true });
                            elementByName.dispatchEvent(changeEvent);

                            console.log(`字段 ${fieldId} (通过name) 已设置为: ${value}`);
                            return true;
                        }

                        console.warn(`未找到字段: ${fieldId}`);
                        return false;

                    } catch (error) {
                        console.error(`设置字段 ${fieldId} 时出错:`, error);
                        return false;
                    }
                }
            }
        });
    </script>
@endpushOnce

