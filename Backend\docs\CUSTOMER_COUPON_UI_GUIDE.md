# 用户优惠券管理界面使用指南

## 功能概述

在Bagisto后台管理系统的客户详情页面中，现在您可以直接为用户发放和管理优惠券。该功能位于右侧组件的地址列表下方。

## 界面位置

**路径**: 后台管理 → 客户管理 → 客户列表 → 查看客户详情 → 右侧面板 → 地址列表下方

## 功能特性

### 1. 优惠券列表显示
- **状态标识**: 不同颜色标签显示优惠券状态
  - 🟢 可用 (active)
  - 🟡 已使用 (used) 
  - 🔴 已过期/已撤销 (expired/revoked)
  - 🟠 即将过期 (7天内过期)

- **详细信息展示**:
  - 优惠券名称
  - 优惠券代码
  - 折扣信息（百分比/固定金额等）
  - 使用情况（已使用次数/总限制次数）
  - 过期时间
  - 发放时间
  - 发放原因
  - 发放人员

### 2. 发放优惠券功能

#### 操作步骤：
1. 点击优惠券区域标题栏右侧的 ➕ 按钮
2. 在弹出的模态框中填写信息：
   - **选择优惠券** (必填): 从下拉列表中选择要发放的优惠券
   - **使用次数限制** (可选): 该用户可使用该优惠券的次数，默认1次
   - **过期时间** (可选): 自定义过期时间，留空则使用优惠券原始过期时间
   - **发放原因** (可选): 记录发放原因，便于后续追踪
3. 点击"发放优惠券"按钮完成发放

#### 验证规则：
- 优惠券必须选择
- 使用次数限制：1-100次
- 过期时间必须是未来日期
- 同一优惠券不能重复发放给同一用户

### 3. 撤销优惠券功能

#### 操作步骤：
1. 在优惠券列表中找到要撤销的优惠券（仅显示"可用"状态的优惠券）
2. 点击"撤销"链接
3. 确认撤销操作
4. 优惠券状态将变更为"已撤销"

#### 注意事项：
- 只有状态为"可用"的优惠券才能撤销
- 撤销操作不可逆
- 系统会自动记录撤销原因和操作人

## 状态说明

| 状态 | 描述 | 颜色标识 | 可操作 |
|------|------|----------|--------|
| active | 优惠券可正常使用 | 绿色 | 可撤销 |
| used | 优惠券已使用完毕 | 黄色 | 无 |
| expired | 优惠券已过期 | 红色 | 无 |
| revoked | 优惠券已被撤销 | 红色 | 无 |

## 权限要求

- **查看优惠券**: 需要客户查看权限
- **发放优惠券**: 需要客户编辑权限
- **撤销优惠券**: 需要客户编辑权限

## 数据安全

- 所有操作都会记录操作人和操作时间
- 撤销操作会记录详细的撤销原因
- 发放历史完整保留，便于审计

## 集成说明

### 前端技术
- Vue.js 3 组件化开发
- Tailwind CSS 样式框架
- Axios HTTP 客户端
- Laravel Blade 模板引擎

### 后端技术
- Laravel 控制器处理业务逻辑
- Repository 模式数据访问
- 服务层处理复杂业务逻辑
- 数据验证和错误处理

### API端点
```
GET    /admin/customers/{id}/coupons          # 获取用户优惠券列表
POST   /admin/customers/{id}/coupons          # 发放优惠券
DELETE /admin/customers/{id}/coupons/{couponId} # 撤销优惠券
GET    /admin/customers/coupons/available     # 获取可用优惠券列表
```

## 错误处理

### 常见错误信息
- "该优惠券已发放给此用户" - 尝试重复发放
- "优惠券不存在" - 选择的优惠券无效
- "撤销失败" - 网络或权限问题
- "发放失败，请重试" - 服务器错误

### 解决方案
1. 刷新页面重试
2. 检查网络连接
3. 确认操作权限
4. 联系系统管理员

## 最佳实践

1. **发放前确认**: 确保选择正确的优惠券和客户
2. **填写原因**: 建议填写详细的发放原因，便于后续管理
3. **定期检查**: 定期查看即将过期的优惠券，提醒用户使用
4. **谨慎撤销**: 撤销操作不可逆，操作前请再次确认

## 扩展功能

### 计划中的增强功能
- 批量发放优惠券
- 优惠券使用统计报表
- 自动过期提醒
- 优惠券模板管理
- 发放审批流程

### 自定义开发
如需要更多功能，可以基于现有架构进行扩展：
- 添加新的优惠券状态
- 自定义发放规则
- 集成第三方通知系统
- 添加更多统计维度 