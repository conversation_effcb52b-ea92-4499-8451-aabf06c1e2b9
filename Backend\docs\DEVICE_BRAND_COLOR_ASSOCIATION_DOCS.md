# 产品品牌-设备-子产品关联功能文档

## 概述

本文档描述了对 `ProductController.php` 中 `getDeviceBrandInfo` 方法的修改，以支持 configurable 产品的品牌、设备型号和子产品之间的关联筛选功能。

## 功能需求

当产品为 configurable 类型时，需要将子产品的 brand 和 device 属性做成关联，方便前端联动筛选。具体场景为：选择产品品牌对应的 device 型号后，需要筛选出对应的子产品。由于颜色和子产品为1对1绑定关系，通过子产品ID就可以从variants中匹配到对应的产品信息（包括颜色）。

## 修改内容

### 1. 主要方法修改

#### `getDeviceBrandInfo($product)` 方法
- **原功能**: 仅从主产品获取 brand 和 device 信息
- **新功能**: 
  - 对于 configurable 产品：从所有子产品中收集 brand、device、color 信息并建立关联
  - 对于 simple 产品：保持原有逻辑不变

#### 新增方法

1. **`getConfigurableDeviceBrandInfo($product)`**
   - 专门处理 configurable 产品的品牌-设备-子产品关联
   - 遍历所有变体产品，收集属性信息
   - 建立三维关联数组：`brand_id -> device_id -> [variant_ids]`

2. **`getSimpleDeviceBrandInfo($product)`**
   - 处理 simple 产品的品牌设备信息
   - 保持原有逻辑

### 2. 返回数据结构

#### 原返回结构
```json
{
    "devices": [...],
    "brands": [...]
}
```

#### 新返回结构
```json
{
    "devices": [...],
    "brands": [...],
    "brand_device_variants": {
        "brand_id": {
            "device_id": [
                "variant_id_1",
                "variant_id_2"
            ]
        }
    }
}
```

### 3. API 响应修改

在产品详情 API 响应中新增 `brand_device_variants` 字段，包含品牌-设备-子产品的完整关联信息。

## 使用场景

### 前端联动筛选流程

1. **选择品牌**: 用户选择某个品牌
2. **筛选设备**: 根据选择的品牌，显示该品牌下的所有设备型号
3. **获取子产品**: 根据选择的品牌和设备，获取对应的子产品ID列表
4. **匹配产品信息**: 通过子产品ID从variants中匹配到完整的产品信息（包括颜色、价格等）

### 示例数据

假设有以下变体产品：
- 变体1 (ID: 101): Apple + iPhone 16 Pro Max + Red
- 变体2 (ID: 102): Apple + iPhone 16 Pro Max + Black
- 变体3 (ID: 103): Samsung + Galaxy S24 + Blue
- 变体4 (ID: 104): Samsung + Galaxy S24 + White

返回的关联数据结构：
```json
{
    "brand_device_variants": {
        "371": {  // Apple brand_id
            "381": [101, 102]  // iPhone 16 Pro Max device_id -> variant_ids
        },
        "372": {  // Samsung brand_id
            "382": [103, 104]  // Galaxy S24 device_id -> variant_ids
        }
    }
}
```

前端可以通过子产品ID从variants数组中获取完整信息：
```json
{
    "variants": [
        {
            "id": 101,
            "sku": "apple-iphone16-red",
            "color": {"id": "1", "name": "Red", "swatch_value": "#FF0000"},
            "price": 999
        },
        {
            "id": 102,
            "sku": "apple-iphone16-black",
            "color": {"id": "4", "name": "Black", "swatch_value": "#000000"},
            "price": 999
        }
    ]
}
```

## 属性ID参考

根据项目记忆：
- **color 属性ID**: 23 (Red=1, Green=2, Yellow=3, Black=4, White=5)
- **brand 属性ID**: 25 (Apple=371, Samsung=372, Huawei=373)
- **device 属性ID**: 33 (多选类型，包含各种手机型号ID，如 iPhone 16 Pro Max=381)

## 测试方法

### 1. 使用测试脚本
```bash
# 测试特定产品的设备品牌信息
php test_device_brand_info.php [product_id]

# 测试API端点
php test_api_endpoint.php [product_id]
```

### 2. API 调用测试
```bash
curl -X POST "http://your-domain/api/product/detail" \
     -H "Content-Type: application/json" \
     -d '{"id": 1}'
```

## 注意事项

1. **性能考虑**: 对于有大量变体的产品，建议考虑缓存机制
2. **数据完整性**: 确保所有变体产品都有完整的 brand、device、color 属性
3. **错误处理**: 方法包含完善的异常处理，确保API稳定性
4. **向后兼容**: 修改保持了对 simple 产品的向后兼容性

## 前端集成建议

### JavaScript 示例
```javascript
// 获取产品详情
const productDetail = await fetch('/api/product/detail', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({id: productId})
}).then(res => res.json());

const { brands, devices, brand_device_variants, variants } = productDetail.data;

// 品牌选择变化时
function onBrandChange(selectedBrandId) {
    const availableDevices = devices.filter(device =>
        brand_device_variants[selectedBrandId] &&
        brand_device_variants[selectedBrandId][device.id]
    );

    // 更新设备选项
    updateDeviceOptions(availableDevices);
}

// 设备选择变化时
function onDeviceChange(selectedBrandId, selectedDeviceId) {
    const variantIds = brand_device_variants[selectedBrandId]?.[selectedDeviceId] || [];

    // 根据子产品ID获取完整的产品信息
    const availableVariants = variants.filter(variant =>
        variantIds.includes(variant.id)
    );

    // 提取颜色信息
    const availableColors = availableVariants.map(variant => variant.color).filter(Boolean);

    // 更新颜色选项和产品信息
    updateColorOptions(availableColors);
    updateVariantOptions(availableVariants);
}
```

