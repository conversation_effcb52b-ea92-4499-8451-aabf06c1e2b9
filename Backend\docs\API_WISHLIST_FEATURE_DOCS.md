# 产品Wishlist功能文档

## 功能概述

已为所有产品API接口添加了wishlist（收藏夹）状态检查功能。现在所有产品数据都会包含`is_in_wishlist`字段，表示当前登录用户是否已将该产品加入收藏夹。

## 技术实现

### 修改的文件

1. **BaseProductFormatter.php** - 添加wishlist检查逻辑
2. **IndexController.php** - 更新构造函数，注入WishlistRepository
3. **CategoryProductController.php** - 更新构造函数，注入WishlistRepository

### 核心功能

#### 1. 自动检测用户登录状态
- 自动检测是否有登录用户（使用sanctum guard）
- 如果未登录，所有产品的`is_in_wishlist`都返回`false`
- 如果已登录，查询用户的wishlist数据

#### 2. 性能优化
- 使用缓存机制，一次性获取当前用户的所有wishlist产品ID
- 避免对每个产品都进行单独的数据库查询
- 只在当前渠道范围内查询wishlist数据

#### 3. 错误处理
- 如果wishlist查询失败，记录警告日志但不中断程序执行
- 默认返回`false`，确保API稳定性

## 产品数据格式

### 新增字段

```json
{
  "id": 1,
  "name": "产品名称",
  "url_key": "product-url",
  "price": 99.99,
  "formatted_price": "$99.99",
  "base_price": 119.99,
  "formatted_base_price": "$119.99",
  "short_description": "产品简介",
  "description": "产品详细描述",
  "base_image": "http://example.com/image.jpg",
  "images": [...],
  "is_new": false,
  "is_featured": true,
  "color": {...},
  "discount": {...},
  "is_in_wishlist": true  // 新增字段：是否已加入收藏夹
}
```

### 字段说明

- **is_in_wishlist**: `boolean`
  - `true`: 当前登录用户已将此产品加入收藏夹
  - `false`: 当前登录用户未将此产品加入收藏夹（或用户未登录）

## 影响的API接口

以下所有返回产品数据的API接口都已包含wishlist状态：

### 1. 首页接口
- **路由**: `GET /api/mlk/index`
- **说明**: 首页的所有产品列表（新品推荐、热销产品等）

### 2. 分类产品接口
- **路由**: `POST /api/mlk/category/by-categories`
- **说明**: 通过分类ID查询产品列表

### 3. 搜索接口
- **路由**: `GET /api/mlk/search`
- **说明**: 产品搜索结果（如果使用了相同的产品格式化器）

### 4. 其他产品列表接口
- 所有使用BaseProductFormatter的接口都会自动包含此功能

## 使用示例

### 请求示例

```bash
# 未登录用户请求
curl -X GET "http://your-domain.com/api/mlk/index" \
  -H "Accept: application/json"

# 登录用户请求
curl -X GET "http://your-domain.com/api/mlk/index" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "homepage": {
      "new_arrivals": {
        "title": "新品推荐",
        "data": [
          {
            "id": 1,
            "name": "iPhone 15 Case",
            "price": 29.99,
            "is_in_wishlist": true,
            "color": {
              "option_id": 5,
              "option_value": "red",
              "option_label": "红色"
            },
            "discount": {
              "has_discount": true,
              "discount_percentage": 25.0
            }
          }
        ]
      }
    }
  }
}
```

## 与现有Wishlist API的关系

### 现有Wishlist操作API

- `GET /api/mlk/wishlist` - 获取收藏夹列表
- `POST /api/mlk/wishlist` - 添加/删除收藏夹商品
- `POST /api/mlk/wishlist/move-to-cart` - 移动到购物车
- `POST /api/mlk/wishlist/delete` - 删除单个商品
- `POST /api/mlk/wishlist/clear` - 清空收藏夹

### 数据一致性

- 产品数据中的`is_in_wishlist`状态与wishlist操作API保持实时同步
- 当用户添加/删除收藏夹商品后，后续的产品列表请求会立即反映最新状态

## 注意事项

### 1. 用户认证
- 必须使用sanctum认证的用户才能获得准确的wishlist状态
- 访客用户所有产品的`is_in_wishlist`都为`false`

### 2. 渠道隔离
- 只检查当前渠道(channel)的wishlist数据
- 不同渠道的wishlist数据相互独立

### 3. 性能考虑
- 首次请求时会加载用户的所有wishlist数据到内存缓存
- 同一个请求周期内的多个产品检查不会重复查询数据库

### 4. 错误处理
- 如果wishlist查询失败，不会影响产品数据的其他字段
- 错误会记录到日志，但API正常返回（wishlist状态为false）

## 测试建议

### 1. 功能测试
- 测试未登录用户：所有产品`is_in_wishlist`应为`false`
- 测试已登录用户：验证实际收藏状态的准确性
- 测试添加/删除收藏后的状态变化

### 2. 性能测试
- 测试大量产品列表的响应时间
- 验证wishlist查询不会显著影响API性能

### 3. 边界情况测试
- 测试用户没有任何收藏夹商品的情况
- 测试wishlist数据损坏或不一致的情况
- 测试网络异常导致wishlist查询失败的情况 