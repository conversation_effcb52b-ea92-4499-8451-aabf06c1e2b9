# MLK 发票管理 API 文档

## 概述
本文档描述了 MLKWebAPI 模块新增的发票管理相关接口，提供完整的发票查询、下载和统计功能。

## 基础信息
- **基础路径**: `/api/mlk/invoices`
- **认证方式**: <PERSON><PERSON> (Sanctum)
- **响应格式**: JSON

## 接口列表

### 1. 获取发票列表
```http
GET /api/mlk/invoices
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| per_page | integer | 否 | 每页数量，默认10，最大50 |
| state | string | 否 | 发票状态筛选 |
| order_by | string | 否 | 排序字段 |
| sort | string | 否 | 排序方向 (asc/desc) |
| order_id | integer | 否 | 按订单ID筛选 |

**发票状态**: `pending`, `pending_payment`, `paid`, `overdue`, `refunded`

**响应示例**:
```json
{
    "success": true,
    "data": {
        "invoices": [
            {
                "id": 1,
                "increment_id": "INV-*********",
                "state": "paid",
                "status_label": "Paid",
                "grand_total": 299.99,
                "formatted_grand_total": "$299.99",
                "created_at": "2024-01-15T10:30:00Z",
                "can_download": true,
                "download_url": "https://domain.com/api/mlk/invoices/1/download",
                "order": {...},
                "items": [...]
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 3,
            "total": 25
        }
    }
}
```

### 2. 获取发票详情
```http
GET /api/mlk/invoices/{id}
```

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | integer | 是 | 发票ID |

**响应示例**:
```json
{
    "success": true,
    "data": {
        "invoice": {
            "id": 1,
            "increment_id": "INV-*********",
            "state": "paid",
            "status_label": "Paid",
            "grand_total": 299.99,
            "sub_total": 249.99,
            "tax_amount": 25.00,
            "shipping_amount": 25.00,
            "discount_amount": 0.00,
            "transaction_id": "TXN123456789",
            "reminders": 0,
            "order": {
                "id": 1,
                "increment_id": "*********",
                "billing_address": {...},
                "shipping_address": {...},
                "payment_method": {...}
            },
            "items": [
                {
                    "id": 1,
                    "sku": "IPHONE-15-PRO",
                    "name": "iPhone 15 Pro",
                    "qty": 1,
                    "price": 999.99,
                    "total": 999.99,
                    "product": {...}
                }
            ]
        }
    }
}
```

### 3. 下载发票PDF
```http
GET /api/mlk/invoices/{id}/download
```

**功能**: 下载发票PDF文件

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | integer | 是 | 发票ID |

**响应**: PDF文件流
- Content-Type: application/pdf
- Content-Disposition: attachment; filename="invoice-INV-*********.pdf"

### 4. 发票状态统计
```http
GET /api/mlk/invoices/stats/status
```

**功能**: 获取用户发票各状态的数量统计

**响应示例**:
```json
{
    "success": true,
    "data": {
        "stats": {
            "pending": 2,
            "pending_payment": 1,
            "paid": 15,
            "overdue": 1,
            "refunded": 0,
            "total": 19
        }
    }
}
```

### 5. 发票金额统计
```http
GET /api/mlk/invoices/stats/amount
```

**功能**: 获取用户发票金额统计信息

**响应示例**:
```json
{
    "success": true,
    "data": {
        "stats": {
            "total_invoiced": 4999.95,
            "total_paid": 4499.95,
            "total_pending": 500.00,
            "total_overdue": 0.00,
            "by_status": {
                "paid": {
                    "count": 15,
                    "total_amount": 4499.95,
                    "formatted_amount": "$4,499.95"
                },
                "pending": {
                    "count": 2,
                    "total_amount": 500.00,
                    "formatted_amount": "$500.00"
                }
            }
        }
    }
}
```

## 发票状态说明

| 状态 | 说明 | 可下载 |
|------|------|--------|
| `pending` | 待处理 | ❌ |
| `pending_payment` | 待付款 | ✅ |
| `paid` | 已付款 | ✅ |
| `overdue` | 逾期 | ✅ |
| `refunded` | 已退款 | ✅ |

## 错误响应
所有接口遵循统一的错误响应格式：

```json
{
    "success": false,
    "message": "错误描述",
    "code": 404
}
```

**常见错误码**:
- `401`: 未认证
- `404`: 发票不存在
- `400`: 请求参数错误
- `500`: 服务器内部错误

## 认证说明
所有接口都需要在请求头中包含认证令牌：

```http
Authorization: Bearer {your_access_token}
```

## 使用示例

### 获取发票列表
```bash
curl -X GET "https://your-domain.com/api/mlk/invoices?page=1&state=paid" \
  -H "Authorization: Bearer your_token" \
  -H "Accept: application/json"
```

### 下载发票PDF
```bash
curl -X GET "https://your-domain.com/api/mlk/invoices/1/download" \
  -H "Authorization: Bearer your_token" \
  -o "invoice.pdf"
```

### 获取发票统计
```bash
curl -X GET "https://your-domain.com/api/mlk/invoices/stats/amount" \
  -H "Authorization: Bearer your_token" \
  -H "Accept: application/json"
```

## 发票PDF特性
- 支持多语言和RTL布局
- 包含完整的订单和发票信息
- 显示发票状态和支付信息
- 包含提醒信息和支付条款
- 自动格式化金额和日期

## 注意事项
1. 只能查看和下载属于当前用户的发票
2. 发票状态为 `pending` 时不可下载
3. 所有金额字段均为浮点数，单位为订单货币
4. 时间字段均为 UTC 时间格式
5. PDF下载需要发票状态允许下载

## 更新日志
- **2024-01-15**: 初始版本，新增完整发票管理功能
- 支持发票列表、详情、下载、统计等功能
- 完善的PDF模板和多语言支持
