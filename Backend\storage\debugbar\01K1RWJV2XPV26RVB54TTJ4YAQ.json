{"__meta": {"id": "01K1RWJV2XPV26RVB54TTJ4YAQ", "datetime": "2025-08-03 22:14:42", "utime": **********.654093, "method": "POST", "uri": "/admin/catalog/products/create", "ip": "127.0.0.1"}, "modules": {"count": 4, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (42)", "Webkul\\Attribute\\Models\\AttributeFamily (1)"], "views": [], "queries": [{"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'color'", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = '2' limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 3.84, "duration_str": "3.84s", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (5)", "Webkul\\Core\\Models\\Locale (21)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 1.87, "duration_str": "1.87s", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 207", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 208", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 209", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 210", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (6)", "Webkul\\Product\\Models\\ProductAttributeValue (30)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `products` where `sku` = '2034100238139'", "duration": 1.9, "duration_str": "1.9s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 208 and `product_attribute_values`.`product_id` is not null", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 208 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 209 and `product_attribute_values`.`product_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 209 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 210 and `product_attribute_values`.`product_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 210 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.35, "duration_str": "350ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 207 and `product_attribute_values`.`product_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1034", "duration": 2.21, "duration_str": "2.21s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1035", "duration": 2.14, "duration_str": "2.14s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1036", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1037", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = '', `weight` = '', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1038", "duration": 4.1, "duration_str": "4.1s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 207 and `products`.`parent_id` is not null", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 208 and `product_attribute_values`.`product_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1039", "duration": 2.2, "duration_str": "2.2s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = '2034100238139-variant-1', `name` = 'Variant 1', `url_key` = '2034100238139-variant-1', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1040", "duration": 2.51, "duration_str": "2.51s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1041", "duration": 2.2, "duration_str": "2.2s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1042", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1043", "duration": 5.27, "duration_str": "5.27s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 209 and `product_attribute_values`.`product_id` is not null", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1044", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = '2034100238139-variant-2', `name` = 'Variant 2', `url_key` = '2034100238139-variant-2', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1045", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1046", "duration": 2.18, "duration_str": "2.18s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1047", "duration": 2.14, "duration_str": "2.14s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1048", "duration": 3.02, "duration_str": "3.02s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 210 and `product_attribute_values`.`product_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 210 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-3', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1049", "duration": 2.14, "duration_str": "2.14s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 210 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-3', `description` = '2034100238139-variant-3', `name` = 'Variant 3', `url_key` = '2034100238139-variant-3', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1050", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 210 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-3', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1051", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 210 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-3', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1052", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 210 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-3', `description` = '', `name` = '', `url_key` = '', `product_number` = '', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '', `special_price` = '', `special_price_from` = '', `special_price_to` = '', `new` = '', `featured` = '', `visible_individually` = '', `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1053", "duration": 2.09, "duration_str": "2.09s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 207 and `products`.`parent_id` is not null", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754255681.95694, "end": **********.662781, "duration": 0.705841064453125, "duration_str": "706ms", "measures": [{"label": "Booting", "start": 1754255681.95694, "relative_start": 0, "end": **********.251533, "relative_end": **********.251533, "duration": 0.*****************, "duration_str": "295ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.251544, "relative_start": 0.****************, "end": **********.662782, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.266584, "relative_start": 0.****************, "end": **********.269049, "relative_end": **********.269049, "duration": 0.0024650096893310547, "duration_str": "2.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.652001, "relative_start": 0.****************, "end": **********.652385, "relative_end": **********.652385, "duration": 0.0003840923309326172, "duration_str": "384μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 112, "nb_statements": 112, "nb_visible_statements": 112, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.17098999999999992, "accumulated_duration_str": "171ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 12 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.287896, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 0.345}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.292516, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.345, "width_percent": 0.135}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.300731, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0.48, "width_percent": 0.135}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.302995, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0.614, "width_percent": 0.146}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.304445, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.76, "width_percent": 0.111}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.308662, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 0.871, "width_percent": 0.135}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.3113449, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 1.006, "width_percent": 0.117}, {"sql": "select count(*) as aggregate from `products` where `sku` = '2034100238139'", "type": "query", "params": [], "bindings": ["2034100238139"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.314014, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 1.123, "width_percent": 1.111}, {"sql": "insert into `products` (`type`, `attribute_family_id`, `sku`, `updated_at`, `created_at`) values ('configurable', '2', '2034100238139', '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": ["configurable", "2", "2034100238139", "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 92}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.322576, "duration": 0.01103, "duration_str": "11.03ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 2.234, "width_percent": 6.451}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 207", "type": "query", "params": [], "bindings": [207], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 92}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.342354, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 8.685, "width_percent": 1.456}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 207)", "type": "query", "params": [], "bindings": [1, 207], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 92}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.345833, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 10.141, "width_percent": 1.252}, {"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "type": "query", "params": [], "bindings": ["sku", "name", "url_key", "short_description", "description", "price", "weight", "status", "tax_category_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 101}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3489668, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 11.392, "width_percent": 1.176}, {"sql": "select * from `attributes` where `code` = 'color'", "type": "query", "params": [], "bindings": ["color"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 208}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 106}], "start": **********.351997, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 12.568, "width_percent": 0.111}, {"sql": "insert into `product_super_attributes` (`attribute_id`, `product_id`) values (23, 207)", "type": "query", "params": [], "bindings": [23, 207], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 112}, {"index": 12, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 13, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.353443, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "Configurable.php:112", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=112", "ajax": false, "filename": "Configurable.php", "line": "112"}, "connection": "mlk", "explain": null, "start_percent": 12.679, "width_percent": 2.281}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 118}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.358578, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 14.96, "width_percent": 1.094}, {"sql": "insert into `products` (`type`, `sku`, `attribute_family_id`, `parent_id`, `updated_at`, `created_at`) values ('simple', '2034100238139-variant-1', '2', 207, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": ["simple", "2034100238139-variant-1", "2", 207, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.3614562, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 16.054, "width_percent": 2.749}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 208", "type": "query", "params": [], "bindings": [208], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.367088, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 18.802, "width_percent": 0.094}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 208)", "type": "query", "params": [], "bindings": [1, 208], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.368081, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 18.896, "width_percent": 1.205}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 208 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [208], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}], "start": **********.372236, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 20.101, "width_percent": 1.181}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (1, null, null, null, null, null, null, null, null, 208, '2034100238139-variant-1', '208|1'), (2, null, null, null, null, null, null, null, 'it', 208, 'Variant 1', 'it|208|2'), (3, null, null, null, null, null, null, null, 'it', 208, '2034100238139-variant-1', 'it|208|3'), (4, null, 'default', null, null, null, '', null, null, 208, null, 'default|208|4'), (8, 1, 'default', null, null, null, null, null, null, 208, null, 'default|208|8'), (9, null, null, null, null, null, null, null, null, 208, '2034100238139-variant-1', '208|9'), (10, null, null, null, null, null, null, null, 'it', 208, '2034100238139-variant-1', 'it|208|10'), (11, null, null, null, null, null, null, null, null, 208, null, '208|11'), (22, null, null, null, null, null, null, null, null, 208, 0, '208|22'), (23, null, null, null, null, null, 1, null, null, 208, null, '208|23')", "type": "query", "params": [], "bindings": [1, null, null, null, null, null, null, null, null, 208, "2034100238139-variant-1", "208|1", 2, null, null, null, null, null, null, null, "it", 208, "Variant 1", "it|208|2", 3, null, null, null, null, null, null, null, "it", 208, "2034100238139-variant-1", "it|208|3", 4, null, "default", null, null, null, "", null, null, 208, null, "default|208|4", 8, 1, "default", null, null, null, null, null, null, 208, null, "default|208|8", 9, null, null, null, null, null, null, null, null, 208, "2034100238139-variant-1", "208|9", 10, null, null, null, null, null, null, null, "it", 208, "2034100238139-variant-1", "it|208|10", 11, null, null, null, null, null, null, null, null, 208, null, "208|11", 22, null, null, null, null, null, null, null, null, 208, 0, "208|22", 23, null, null, null, null, null, 1, null, null, 208, null, "208|23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.376689, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 21.282, "width_percent": 1.585}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 208 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [208], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 221}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.3804939, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 22.867, "width_percent": 0.146}, {"sql": "insert into `products` (`type`, `sku`, `attribute_family_id`, `parent_id`, `updated_at`, `created_at`) values ('simple', '2034100238139-variant-2', '2', 207, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": ["simple", "2034100238139-variant-2", "2", 207, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.381823, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 23.013, "width_percent": 1.234}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 209", "type": "query", "params": [], "bindings": [209], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.384778, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 24.247, "width_percent": 0.111}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 209)", "type": "query", "params": [], "bindings": [1, 209], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.385729, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 24.358, "width_percent": 1.222}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 209 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [209], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}], "start": **********.388795, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 25.58, "width_percent": 0.123}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (1, null, null, null, null, null, null, null, null, 209, '2034100238139-variant-2', '209|1'), (2, null, null, null, null, null, null, null, 'it', 209, 'Variant 2', 'it|209|2'), (3, null, null, null, null, null, null, null, 'it', 209, '2034100238139-variant-2', 'it|209|3'), (4, null, 'default', null, null, null, '', null, null, 209, null, 'default|209|4'), (8, 1, 'default', null, null, null, null, null, null, 209, null, 'default|209|8'), (9, null, null, null, null, null, null, null, null, 209, '2034100238139-variant-2', '209|9'), (10, null, null, null, null, null, null, null, 'it', 209, '2034100238139-variant-2', 'it|209|10'), (11, null, null, null, null, null, null, null, null, 209, null, '209|11'), (22, null, null, null, null, null, null, null, null, 209, 0, '209|22'), (23, null, null, null, null, null, 2, null, null, 209, null, '209|23')", "type": "query", "params": [], "bindings": [1, null, null, null, null, null, null, null, null, 209, "2034100238139-variant-2", "209|1", 2, null, null, null, null, null, null, null, "it", 209, "Variant 2", "it|209|2", 3, null, null, null, null, null, null, null, "it", 209, "2034100238139-variant-2", "it|209|3", 4, null, "default", null, null, null, "", null, null, 209, null, "default|209|4", 8, 1, "default", null, null, null, null, null, null, 209, null, "default|209|8", 9, null, null, null, null, null, null, null, null, 209, "2034100238139-variant-2", "209|9", 10, null, null, null, null, null, null, null, "it", 209, "2034100238139-variant-2", "it|209|10", 11, null, null, null, null, null, null, null, null, 209, null, "209|11", 22, null, null, null, null, null, null, null, null, 209, 0, "209|22", 23, null, null, null, null, null, 2, null, null, 209, null, "209|23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.391104, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 25.703, "width_percent": 2.222}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 209 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [209], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 221}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.39582, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 27.926, "width_percent": 0.111}, {"sql": "insert into `products` (`type`, `sku`, `attribute_family_id`, `parent_id`, `updated_at`, `created_at`) values ('simple', '2034100238139-variant-3', '2', 207, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": ["simple", "2034100238139-variant-3", "2", 207, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.3969688, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:136", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=136", "ajax": false, "filename": "AbstractType.php", "line": "136"}, "connection": "mlk", "explain": null, "start_percent": 28.037, "width_percent": 1.263}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 210", "type": "query", "params": [], "bindings": [210], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.400108, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 29.3, "width_percent": 0.099}, {"sql": "insert into `product_channels` (`channel_id`, `product_id`) values (1, 210)", "type": "query", "params": [], "bindings": [1, 210], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 206}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.4010718, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:138", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=138", "ajax": false, "filename": "AbstractType.php", "line": "138"}, "connection": "mlk", "explain": null, "start_percent": 29.399, "width_percent": 1.176}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 210 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [210], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}], "start": **********.404006, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 30.575, "width_percent": 0.111}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (1, null, null, null, null, null, null, null, null, 210, '2034100238139-variant-3', '210|1'), (2, null, null, null, null, null, null, null, 'it', 210, 'Variant 3', 'it|210|2'), (3, null, null, null, null, null, null, null, 'it', 210, '2034100238139-variant-3', 'it|210|3'), (4, null, 'default', null, null, null, '', null, null, 210, null, 'default|210|4'), (8, 1, 'default', null, null, null, null, null, null, 210, null, 'default|210|8'), (9, null, null, null, null, null, null, null, null, 210, '2034100238139-variant-3', '210|9'), (10, null, null, null, null, null, null, null, 'it', 210, '2034100238139-variant-3', 'it|210|10'), (11, null, null, null, null, null, null, null, null, 210, null, '210|11'), (22, null, null, null, null, null, null, null, null, 210, 0, '210|22'), (23, null, null, null, null, null, 3, null, null, 210, null, '210|23')", "type": "query", "params": [], "bindings": [1, null, null, null, null, null, null, null, null, 210, "2034100238139-variant-3", "210|1", 2, null, null, null, null, null, null, null, "it", 210, "Variant 3", "it|210|2", 3, null, null, null, null, null, null, null, "it", 210, "2034100238139-variant-3", "it|210|3", 4, null, "default", null, null, null, "", null, null, 210, null, "default|210|4", 8, 1, "default", null, null, null, null, null, null, 210, null, "default|210|8", 9, null, null, null, null, null, null, null, null, 210, "2034100238139-variant-3", "210|9", 10, null, null, null, null, null, null, null, "it", 210, "2034100238139-variant-3", "it|210|10", 11, null, null, null, null, null, null, null, null, 210, null, "210|11", 22, null, null, null, null, null, null, null, null, 210, 0, "210|22", 23, null, null, null, null, null, 3, null, null, 210, null, "210|23"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 217}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.406283, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 30.686, "width_percent": 1.445}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 210 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [210], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 221}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 116}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 56}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 113}], "start": **********.409794, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 32.131, "width_percent": 0.205}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 763}], "start": **********.413202, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Flat.php:60", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=60", "ajax": false, "filename": "Flat.php", "line": "60"}, "connection": "mlk", "explain": null, "start_percent": 32.335, "width_percent": 0.439}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 207}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 132}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}], "start": **********.414889, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 32.774, "width_percent": 0.099}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 207}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 132}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}], "start": **********.415869, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 32.873, "width_percent": 2.246}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 207", "type": "query", "params": [], "bindings": [207], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.420832, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 35.119, "width_percent": 0.135}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 207 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [207], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.422334, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 35.254, "width_percent": 0.123}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.42481, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 35.376, "width_percent": 1.135}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [207, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.428384, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 36.511, "width_percent": 0.187}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 29, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 34, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.4294758, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 26, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 36.698, "width_percent": 0.351}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (207, 'default', 'en', 'configurable', '2034100238139', '2', '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [207, "default", "en", "configurable", "2034100238139", "2", "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.430757, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 37.049, "width_percent": 1.45}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1034", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:14:42", 1034], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.435996, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 38.499, "width_percent": 1.292}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": [207, "default", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.4392319, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 39.792, "width_percent": 0.146}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (207, 'default', 'it', 'configurable', '2034100238139', '2', '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [207, "default", "it", "configurable", "2034100238139", "2", "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.440252, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 39.938, "width_percent": 2.854}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1035", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:14:42", 1035], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.447162, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 42.792, "width_percent": 1.252}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'de') limit 1", "type": "query", "params": [], "bindings": [207, "default", "de"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.4502878, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 44.044, "width_percent": 0.14}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (207, 'default', 'de', 'configurable', '2034100238139', '2', '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [207, "default", "de", "configurable", "2034100238139", "2", "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.4514, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 44.184, "width_percent": 1.257}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1036", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:14:42", 1036], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.455589, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 45.441, "width_percent": 1.24}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'fr') limit 1", "type": "query", "params": [], "bindings": [207, "default", "fr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.4586458, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 46.681, "width_percent": 0.146}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (207, 'default', 'fr', 'configurable', '2034100238139', '2', '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [207, "default", "fr", "configurable", "2034100238139", "2", "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.459748, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 46.827, "width_percent": 1.252}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1037", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:14:42", 1037], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.463976, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 48.079, "width_percent": 1.234}, {"sql": "select * from `product_flat` where (`product_id` = 207 and `channel` = 'default' and `locale` = 'gr') limit 1", "type": "query", "params": [], "bindings": [207, "default", "gr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.467053, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 49.313, "width_percent": 0.129}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (207, 'default', 'gr', 'configurable', '2034100238139', '2', '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [207, "default", "gr", "configurable", "2034100238139", "2", "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.468081, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 49.441, "width_percent": 1.199}, {"sql": "update `product_flat` set `short_description` = null, `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = null, `weight` = null, `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1038", "type": "query", "params": [], "bindings": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "2025-08-03 22:14:42", 1038], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.472251, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 50.64, "width_percent": 2.398}, {"sql": "select * from `products` where `products`.`parent_id` = 207 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [207], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 119}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.4778879, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Flat.php:119", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=119", "ajax": false, "filename": "Flat.php", "line": "119"}, "connection": "mlk", "explain": null, "start_percent": 53.038, "width_percent": 0.14}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 208", "type": "query", "params": [], "bindings": [208], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.4793189, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 53.179, "width_percent": 0.152}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 208 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [208], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.480517, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 53.331, "width_percent": 0.135}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.482775, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 53.465, "width_percent": 0.135}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [208, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.483835, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 53.6, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (208, 'default', 'en', 'simple', '2034100238139-variant-1', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [208, "default", "en", "simple", "2034100238139-variant-1", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.484744, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 53.711, "width_percent": 1.24}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1039", "type": "query", "params": [], "bindings": ["2034100238139-variant-1", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1039], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.489586, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 54.951, "width_percent": 1.287}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": [208, "default", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.492821, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 56.237, "width_percent": 0.181}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (208, 'default', 'it', 'simple', '2034100238139-variant-1', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [208, "default", "it", "simple", "2034100238139-variant-1", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.493952, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 56.419, "width_percent": 1.246}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = '2034100238139-variant-1', `name` = 'Variant 1', `url_key` = '2034100238139-variant-1', `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1040", "type": "query", "params": [], "bindings": ["2034100238139-variant-1", "2034100238139-variant-1", "Variant 1", "2034100238139-variant-1", null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1040], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.498988, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 57.664, "width_percent": 1.468}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'de') limit 1", "type": "query", "params": [], "bindings": [208, "default", "de"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.5025768, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 59.132, "width_percent": 0.146}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (208, 'default', 'de', 'simple', '2034100238139-variant-1', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [208, "default", "de", "simple", "2034100238139-variant-1", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.503631, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 59.278, "width_percent": 1.228}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1041", "type": "query", "params": [], "bindings": ["2034100238139-variant-1", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1041], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.508311, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 60.506, "width_percent": 1.287}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'fr') limit 1", "type": "query", "params": [], "bindings": [208, "default", "fr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.511515, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 61.793, "width_percent": 0.129}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (208, 'default', 'fr', 'simple', '2034100238139-variant-1', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [208, "default", "fr", "simple", "2034100238139-variant-1", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.512451, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 61.922, "width_percent": 1.234}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1042", "type": "query", "params": [], "bindings": ["2034100238139-variant-1", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1042], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.517367, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 63.156, "width_percent": 1.234}, {"sql": "select * from `product_flat` where (`product_id` = 208 and `channel` = 'default' and `locale` = 'gr') limit 1", "type": "query", "params": [], "bindings": [208, "default", "gr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.520477, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 64.39, "width_percent": 0.123}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (208, 'default', 'gr', 'simple', '2034100238139-variant-1', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [208, "default", "gr", "simple", "2034100238139-variant-1", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.521486, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 64.513, "width_percent": 1.216}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-1', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1043", "type": "query", "params": [], "bindings": ["2034100238139-variant-1", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1043], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.526461, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 65.729, "width_percent": 3.082}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 209", "type": "query", "params": [], "bindings": [209], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.5328221, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 68.811, "width_percent": 0.175}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 209 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [209], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5339918, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 68.986, "width_percent": 0.146}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.5363789, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 69.133, "width_percent": 0.129}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [209, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.53738, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 69.261, "width_percent": 0.123}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (209, 'default', 'en', 'simple', '2034100238139-variant-2', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [209, "default", "en", "simple", "2034100238139-variant-2", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.538388, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 69.384, "width_percent": 1.234}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1044", "type": "query", "params": [], "bindings": ["2034100238139-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1044], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.543336, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 70.618, "width_percent": 1.234}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": [209, "default", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.546386, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 71.852, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (209, 'default', 'it', 'simple', '2034100238139-variant-2', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [209, "default", "it", "simple", "2034100238139-variant-2", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.5472841, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 71.969, "width_percent": 1.216}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = '2034100238139-variant-2', `name` = 'Variant 2', `url_key` = '2034100238139-variant-2', `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1045", "type": "query", "params": [], "bindings": ["2034100238139-variant-2", "2034100238139-variant-2", "Variant 2", "2034100238139-variant-2", null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1045], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5522459, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 73.186, "width_percent": 1.246}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'de') limit 1", "type": "query", "params": [], "bindings": [209, "default", "de"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.5552459, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 74.431, "width_percent": 0.105}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (209, 'default', 'de', 'simple', '2034100238139-variant-2', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [209, "default", "de", "simple", "2034100238139-variant-2", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.556107, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 74.537, "width_percent": 2.45}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1046", "type": "query", "params": [], "bindings": ["2034100238139-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1046], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.562996, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 76.987, "width_percent": 1.275}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'fr') limit 1", "type": "query", "params": [], "bindings": [209, "default", "fr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.566147, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 78.262, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (209, 'default', 'fr', 'simple', '2034100238139-variant-2', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [209, "default", "fr", "simple", "2034100238139-variant-2", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.567179, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 78.373, "width_percent": 1.24}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1047", "type": "query", "params": [], "bindings": ["2034100238139-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1047], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.572094, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 79.613, "width_percent": 1.252}, {"sql": "select * from `product_flat` where (`product_id` = 209 and `channel` = 'default' and `locale` = 'gr') limit 1", "type": "query", "params": [], "bindings": [209, "default", "gr"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.575109, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 80.864, "width_percent": 0.14}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (209, 'default', 'gr', 'simple', '2034100238139-variant-2', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [209, "default", "gr", "simple", "2034100238139-variant-2", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.576066, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 81.005, "width_percent": 1.246}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-2', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1048", "type": "query", "params": [], "bindings": ["2034100238139-variant-2", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1048], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.580703, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 82.25, "width_percent": 1.766}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 210", "type": "query", "params": [], "bindings": [210], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.584886, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 84.017, "width_percent": 0.158}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 210 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [210], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.58603, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "mlk", "explain": null, "start_percent": 84.175, "width_percent": 0.135}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.588326, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 84.309, "width_percent": 0.129}, {"sql": "select * from `product_flat` where (`product_id` = 210 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [210, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.5893579, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 84.438, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (210, 'default', 'en', 'simple', '2034100238139-variant-3', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [210, "default", "en", "simple", "2034100238139-variant-3", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.590416, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 84.555, "width_percent": 1.216}, {"sql": "update `product_flat` set `short_description` = '2034100238139-variant-3', `description` = null, `name` = null, `url_key` = null, `product_number` = null, `meta_title` = null, `meta_keywords` = null, `meta_description` = null, `price` = null, `special_price` = null, `special_price_from` = null, `special_price_to` = null, `new` = null, `featured` = null, `visible_individually` = null, `status` = 1, `weight` = '0', `product_flat`.`updated_at` = '2025-08-03 22:14:42' where `id` = 1049", "type": "query", "params": [], "bindings": ["2034100238139-variant-3", null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, "0", "2025-08-03 22:14:42", 1049], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.595327, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "mlk", "explain": null, "start_percent": 85.771, "width_percent": 1.252}, {"sql": "select * from `product_flat` where (`product_id` = 210 and `channel` = 'default' and `locale` = 'it') limit 1", "type": "query", "params": [], "bindings": [210, "default", "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.598543, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 87.023, "width_percent": 0.123}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (210, 'default', 'it', 'simple', '2034100238139-variant-3', 2, '2025-08-03 22:14:42', '2025-08-03 22:14:42')", "type": "query", "params": [], "bindings": [210, "default", "it", "simple", "2034100238139-variant-3", 2, "2025-08-03 22:14:42", "2025-08-03 22:14:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 27, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 120}, {"index": 28, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 37}, {"index": 33, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 121}], "start": **********.599594, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 87.145, "width_percent": 1.211}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.604592, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.356, "width_percent": 1.246}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.606948, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.602, "width_percent": 0.117}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.607355, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.719, "width_percent": 1.409}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.612201, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.128, "width_percent": 1.24}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.614494, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.368, "width_percent": 0.099}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.614845, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.467, "width_percent": 1.211}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.619015, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.678, "width_percent": 1.246}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.621341, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.924, "width_percent": 0.111}, {"sql": "insert into `product_flat` (`product_id`, `channel`, `locale`, `type`, `sku`, `attribute_family_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.621721, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.035, "width_percent": 1.181}, {"sql": "update `product_flat` set `short_description` = ?, `description` = ?, `name` = ?, `url_key` = ?, `product_number` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `price` = ?, `special_price` = ?, `special_price_from` = ?, `special_price_to` = ?, `new` = ?, `featured` = ?, `visible_individually` = ?, `status` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.625899, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.216, "width_percent": 1.222}, {"sql": "select * from `products` where `products`.`parent_id` = ? and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.628176, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 97.438, "width_percent": 0.129}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.642364, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 97.567, "width_percent": 2.433}]}, "models": {"data": {"Webkul\\Attribute\\Models\\Attribute": {"value": 42, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductAttributeValue": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}}, "count": 107, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/admin/catalog/products/create", "action_name": "admin.catalog.products.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@store", "uri": "POST admin/catalog/products/create", "controller": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@store<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=87\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/catalog/products", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=87\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php:87-130</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "715ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1713043757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1713043757\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-518674633 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">configurable</span>\"\n  \"<span class=sf-dump-key>attribute_family_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">2034100238139</span>\"\n  \"<span class=sf-dump-key>super_attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>color</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-num>3</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518674633\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1884629601 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6IjJ5YkJ2czlJRm9KVEdzS1BTR0pZUVE9PSIsInZhbHVlIjoiT2kva3NJNFRscENJa2Q5eGQzSzZwcXdHaHpKemhZbGVWQjNJK3llVHl1VVFMV2t0blQ2dFpYSENKcGFsa3dOZyIsIm1hYyI6IjUzYjJjMTQ2MDNiOTAwYTA5YTA0ZjcxYmI0MzUxNWNmMjk1Njk1MDA0NjQ2MzkxNDg4MTgzMTNjM2NlYjkzYmIiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IlM3Rm01ZGRyTDNsdloyVjhySnFUTFE9PSIsInZhbHVlIjoiQmQ3MW5QaXYxZC9SZWFsZ3h5ZEk1S1EyZmpXRkNkMkZPQ2cyc3RaVGVzVFlkSTJPNU95KzFRQ2g2NG1hc0lMUSt3cUk1MW53cEpMWU1BOXc2dWtmSmhoRGdmeEpQWm9ZbEJzZG9vTmRDZjJUOC9TWFdBSVJtc1ZoYkczYmVxZE8iLCJtYWMiOiI1ZjY2ZWM4Y2IzYzczMzY2Zjg4ZmVlNzFiNjI4Nzc3YzA5ZmQ0NWFiODE3YjBiYTRmZmE2ZWZiNjdlNjJkNTkzIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjFRRGFUeG5BdzJYUnI5YlVzSzlOVUE9PSIsInZhbHVlIjoiTUJRMEJDYW5rY0F0dE4xZjBtSzJ6MTFFVEhxK1RBTkE3NSticEFiVlhzdEcxZmdNS0t2OHhlZVlGaXRNYmpVK3NGR3RzaWczOEFVdlpXWS9yOW1ybzhIc3lEaWt5UzJ1M2I1QmxDWG51RTJkV0ZWNU8xR0ZuSytQR3VBWm9rT0ciLCJtYWMiOiJiM2NkYTM4Y2I3ZmYwOTIyMmE3YTJmZWQ4MDdmN2EwZWM0NDhhMTU2Y2Y2MjJhZDU5M2ZiMTFmZDEwMzNiMDQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://mlk.test/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlM3Rm01ZGRyTDNsdloyVjhySnFUTFE9PSIsInZhbHVlIjoiQmQ3MW5QaXYxZC9SZWFsZ3h5ZEk1S1EyZmpXRkNkMkZPQ2cyc3RaVGVzVFlkSTJPNU95KzFRQ2g2NG1hc0lMUSt3cUk1MW53cEpMWU1BOXc2dWtmSmhoRGdmeEpQWm9ZbEJzZG9vTmRDZjJUOC9TWFdBSVJtc1ZoYkczYmVxZE8iLCJtYWMiOiI1ZjY2ZWM4Y2IzYzczMzY2Zjg4ZmVlNzFiNjI4Nzc3YzA5ZmQ0NWFiODE3YjBiYTRmZmE2ZWZiNjdlNjJkNTkzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">108</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884629601\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2057450266 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jxmpOQ2BAfhFaqxfLAYWsgCe5beWeDeiyQktko9E</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JEicZFJies5owWh90sx51Kroy2fKDnW4PkLRo5Wg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057450266\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-933008045 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 03 Aug 2025 21:14:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933008045\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1701828271 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jxmpOQ2BAfhFaqxfLAYWsgCe5beWeDeiyQktko9E</span>\"\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://mlk.test/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#20135;&#21697;&#21019;&#24314;&#25104;&#21151;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701828271\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/admin/catalog/products/create", "action_name": "admin.catalog.products.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@store"}, "badge": null}}