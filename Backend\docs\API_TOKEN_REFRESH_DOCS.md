# Token刷新接口文档

## 接口说明
Token刷新接口用于在用户token即将过期或需要更新时，安全地刷新用户的访问令牌。旧的token会被立即作废，生成新的token返回给客户端。

## 接口信息
- **接口路径**: `POST /api/mlk/customer/refresh-token`
- **认证方式**: Bear<PERSON> (Sanctum)
- **请求方式**: POST

## 请求头
```
Authorization: Bearer {current_token}
Content-Type: application/json
Accept: application/json
```

## 请求参数
无需传递任何参数

## 响应格式

### 成功响应 (200)
```json
{
    "success": true,
    "message": "Token刷新成功",
    "data": {
        "token": "new_access_token_here",
        "expires_at": "2024-01-01T12:00:00.000000Z",
        "customer": {
            "id": 1,
            "first_name": "张",
            "last_name": "三",
            "email": "zhang<PERSON>@example.com",
            "phone": "13800138000",
            "gender": "male",
            "date_of_birth": "1990-01-01",
            "is_verified": true,
            "is_suspended": false
        }
    }
}
```

### 失败响应 (401)
```json
{
    "success": false,
    "message": "未授权访问",
    "data": []
}
```

### 错误响应 (500)
```json
{
    "success": false,
    "message": "服务器内部错误",
    "data": []
}
```

## 使用说明

1. **何时使用**: 
   - Token即将过期时
   - 定期刷新token以提高安全性
   - 用户长时间使用应用时

2. **安全特性**:
   - 旧token立即失效，无法再次使用
   - 新token使用相同的过期策略
   - 需要有效的当前token才能刷新

3. **客户端处理**:
   - 收到新token后立即更新本地存储
   - 更新所有后续请求的Authorization头
   - 处理刷新失败的情况（重新登录）

## 示例代码

### JavaScript (Axios)
```javascript
// 刷新token
async function refreshToken() {
    try {
        const response = await axios.post('/api/mlk/customer/refresh-token', {}, {
            headers: {
                'Authorization': `Bearer ${currentToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.data.success) {
            const newToken = response.data.data.token;
            const expiresAt = response.data.data.expires_at;
            
            // 更新本地存储的token
            localStorage.setItem('access_token', newToken);
            if (expiresAt) {
                localStorage.setItem('token_expires_at', expiresAt);
            }
            
            // 更新axios默认headers
            axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
            
            console.log('Token刷新成功');
            return newToken;
        }
    } catch (error) {
        console.error('Token刷新失败:', error);
        // 重定向到登录页面
        window.location.href = '/login';
        throw error;
    }
}
```

### PHP (cURL)
```php
$currentToken = 'your_current_token_here';

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => 'http://your-domain.com/api/mlk/customer/refresh-token',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_HTTPHEADER => [
        'Authorization: Bearer ' . $currentToken,
        'Content-Type: application/json',
        'Accept: application/json'
    ],
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data['success']) {
        $newToken = $data['data']['token'];
        $expiresAt = $data['data']['expires_at'];
        
        // 保存新token
        $_SESSION['access_token'] = $newToken;
        if ($expiresAt) {
            $_SESSION['token_expires_at'] = $expiresAt;
        }
        
        echo "Token刷新成功: " . $newToken;
    }
} else {
    echo "Token刷新失败";
    // 处理失败情况
}
```

## 注意事项

1. **一次性使用**: 每个token只能刷新一次，刷新后旧token立即失效
2. **并发请求**: 避免同时发起多个刷新请求
3. **错误处理**: 刷新失败时应引导用户重新登录
4. **过期时间**: 如果配置中设置了过期时间，新token将使用相同的过期策略
5. **安全建议**: 建议在token过期前10-15分钟进行刷新

## 相关接口
- [用户登录接口](API_CUSTOMER_REGISTER.md)
- [用户注销接口](#logout-api)
- [用户信息接口](#profile-api) 