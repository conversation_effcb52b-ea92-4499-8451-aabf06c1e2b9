# 用户订单查询 API 接口文档

## 概述

本文档描述了MLK Web API中用户订单查询相关的接口功能，包括订单列表查询和订单详情查询。

**文档特色**：
- 📝 **详细字段说明**: 每个返回字段都有完整的类型和说明
- 📊 **完整数据结构**: 包含所有可能的响应字段
- 💡 **实用示例**: 提供真实的请求和响应示例
- 🔧 **最佳实践**: 前端开发的实用建议

### 🛍️ 产品信息特性

订单查询接口返回完整的产品信息，包括：
- **产品基本信息**: ID、名称、SKU、类型、价格等
- **产品图片**: 完整的图片列表和基础图片URL
- **订单商品详情**: 数量、重量、小计等信息
- **价格格式化**: 支持货币格式化显示

## 🔐 认证要求

所有订单查询接口都需要用户认证，需在请求头中携带Bearer Token：

```
Authorization: Bearer {access_token}
```

## 📋 接口列表

### 1. 获取用户订单列表

**接口地址**: `GET /api/mlk/dashboard/orders`

**接口描述**: 获取当前登录用户的订单列表，支持分页、排序和状态筛选

#### 📥 请求参数

| 参数名称 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|-------|
| page | integer | 否 | 页码，默认为1 | 1 |
| per_page | integer | 否 | 每页数量，默认10，最大50 | 15 |
| status | string | 否 | 订单状态筛选 | completed |
| order_by | string | 否 | 排序字段，默认created_at | created_at |
| sort | string | 否 | 排序方向，默认desc | desc |

#### 🎯 订单状态枚举值

- `pending` - 待处理
- `processing` - 处理中  
- `completed` - 已完成
- `canceled` - 已取消
- `closed` - 已关闭
- `fraud` - 欺诈

#### 📊 排序字段枚举值

- `id` - 订单ID
- `increment_id` - 订单编号
- `status` - 状态
- `created_at` - 创建时间
- `grand_total` - 订单总金额

#### 📤 响应字段说明

**基础响应结构**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| success | boolean | 请求是否成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |

**订单基本信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| id | integer | 订单内部ID |
| increment_id | string | 订单编号（用户可见） |
| status | string | 订单状态代码 |
| status_label | string | 订单状态显示名称 |
| channel_name | string | 销售渠道名称 |
| is_guest | boolean | 是否为游客订单 |
| customer_email | string | 客户邮箱 |
| customer_full_name | string | 客户全名 |
| shipping_method | string | 运输方式代码 |
| shipping_title | string | 运输方式名称 |
| shipping_description | string | 运输方式描述 |
| coupon_code | string\|null | 优惠券代码 |
| is_gift | boolean | 是否为礼品订单 |
| total_item_count | integer | 商品种类总数 |
| total_qty_ordered | integer | 商品数量总计 |
| currency_code | string | 订单货币代码 |

**金额信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| grand_total | decimal | 订单总金额 |
| base_grand_total | decimal | 基础货币总金额 |
| grand_total_invoiced | decimal | 已开票总金额 |
| grand_total_refunded | decimal | 已退款总金额 |
| sub_total | decimal | 商品小计 |
| base_sub_total | decimal | 基础货币商品小计 |
| tax_amount | decimal | 税费金额 |
| base_tax_amount | decimal | 基础货币税费 |
| discount_amount | decimal | 折扣金额 |
| base_discount_amount | decimal | 基础货币折扣 |
| shipping_amount | decimal | 运费金额 |
| base_shipping_amount | decimal | 基础货币运费 |

**时间信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| created_at | datetime | 订单创建时间（ISO格式） |
| updated_at | datetime | 订单更新时间（ISO格式） |
| datetime | string | 人性化时间显示（相对时间） |

**订单商品信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| items | array | 订单商品列表 |
| items[].id | integer | 订单商品ID |
| items[].sku | string | 商品SKU |
| items[].name | string | 商品名称 |
| items[].qty_ordered | integer | 订购数量 |
| items[].qty_shipped | integer | 已发货数量 |
| items[].qty_invoiced | integer | 已开票数量 |
| items[].qty_canceled | integer | 已取消数量 |
| items[].qty_refunded | integer | 已退款数量 |
| items[].price | decimal | 单价 |
| items[].total | decimal | 商品小计 |
| items[].weight | decimal | 单个商品重量 |
| items[].product_id | integer | 产品ID |

**产品详细信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| items[].product | object\|null | 产品详细信息 |
| items[].product.id | integer | 产品ID |
| items[].product.name | string | 产品名称 |
| items[].product.type | string | 产品类型（simple/configurable等） |
| items[].product.sku | string | 产品SKU |
| items[].product.base_image_url | string\|null | 产品主图URL |
| items[].product.images | array | 产品图片列表 |
| items[].product.images[].id | integer | 图片ID |
| items[].product.images[].url | string | 图片完整URL |
| items[].product.images[].path | string | 图片存储路径 |
| items[].product.price | decimal | 产品当前价格 |
| items[].product.formatted_price | string | 格式化价格显示 |

**地址信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| billing_address | object\|null | 账单地址信息 |
| billing_address.first_name | string | 名 |
| billing_address.last_name | string | 姓 |
| billing_address.address | string | 详细地址 |
| billing_address.city | string | 城市 |
| billing_address.state | string | 州/省 |
| billing_address.country | string | 国家代码 |
| billing_address.postcode | string | 邮政编码 |
| billing_address.phone | string | 联系电话 |

**支付信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| payment_method | object\|null | 支付方式信息 |
| payment_method.method | string | 支付方式代码 |
| payment_method.method_title | string | 支付方式名称 |

**分页信息**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| pagination | object | 分页信息 |
| pagination.current_page | integer | 当前页码 |
| pagination.last_page | integer | 总页数 |
| pagination.per_page | integer | 每页数量 |
| pagination.total | integer | 总记录数 |
| pagination.from | integer | 当前页起始记录号 |
| pagination.to | integer | 当前页结束记录号 |

#### 📤 响应示例

**说明**: 以下示例展示了一个包含2个商品的已完成订单，包含完整的产品信息、地址和支付信息。

```json
{
    "success": true,      // 请求成功
    "message": "Success", // 响应消息
    "data": {
        "orders": [
            {
                "id": 1,
                "increment_id": "ORD-A123456",
                "status": "completed",
                "status_label": "Completed",
                "channel_name": "Default",
                "is_guest": false,
                "customer_email": "<EMAIL>",
                "customer_full_name": "John Doe",
                "shipping_method": "flatrate_flatrate",
                "shipping_title": "Flat Rate",
                "shipping_description": "Flat Rate Shipping",
                "coupon_code": null,
                "is_gift": false,
                "total_item_count": 2,
                "total_qty_ordered": 3,
                "currency_code": "USD",
                "grand_total": 156.89,
                "base_grand_total": 156.89,
                "grand_total_invoiced": 156.89,
                "grand_total_refunded": 0.00,
                "sub_total": 140.00,
                "base_sub_total": 140.00,
                "tax_amount": 11.20,
                "base_tax_amount": 11.20,
                "discount_amount": 0.00,
                "base_discount_amount": 0.00,
                "shipping_amount": 5.69,
                "base_shipping_amount": 5.69,
                "created_at": "2024-01-15T10:30:00.000000Z",
                "updated_at": "2024-01-15T14:20:00.000000Z",
                "datetime": "2 hours ago",
                "items": [
                    {
                        "id": 1,
                        "sku": "PROD001",
                        "name": "Sample Product 1",
                        "qty_ordered": 2,
                        "qty_shipped": 2,
                        "qty_invoiced": 2,
                        "qty_canceled": 0,
                        "qty_refunded": 0,
                        "price": 50.00,
                        "total": 100.00,
                        "weight": 1.5,
                        "product_id": 1,
                        "product": {
                            "id": 1,
                            "name": "Sample Product 1",
                            "type": "simple",
                            "sku": "PROD001",
                            "base_image_url": "http://example.com/cache/original/product/1/image1.webp",
                            "images": [
                                {
                                    "id": 1,
                                    "url": "http://example.com/storage/product/1/image1.webp",
                                    "path": "product/1/image1.webp"
                                },
                                {
                                    "id": 2,
                                    "url": "http://example.com/storage/product/1/image2.webp",
                                    "path": "product/1/image2.webp"
                                }
                            ],
                            "price": 50.00,
                            "formatted_price": "$50.00"
                        }
                    },
                    {
                        "id": 2,
                        "sku": "PROD002",
                        "name": "Sample Product 2",
                        "qty_ordered": 1,
                        "qty_shipped": 1,
                        "qty_invoiced": 1,
                        "qty_canceled": 0,
                        "qty_refunded": 0,
                        "price": 40.00,
                        "total": 40.00,
                        "weight": 0.8,
                        "product_id": 2,
                        "product": {
                            "id": 2,
                            "name": "Sample Product 2",
                            "type": "simple",
                            "sku": "PROD002",
                            "base_image_url": "http://example.com/cache/original/product/2/image1.webp",
                            "images": [
                                {
                                    "id": 3,
                                    "url": "http://example.com/storage/product/2/image1.webp",
                                    "path": "product/2/image1.webp"
                                }
                            ],
                            "price": 40.00,
                            "formatted_price": "$40.00"
                        }
                    }
                ],
                "items_count": 2,
                "billing_address": {
                    "first_name": "John",
                    "last_name": "Doe",
                    "address": "123 Main Street",
                    "city": "New York",
                    "state": "NY",
                    "country": "US",
                    "postcode": "10001",
                    "phone": "+1234567890"
                },
                "payment_method": {
                    "method": "cashondelivery",
                    "method_title": "Cash On Delivery"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 3,
            "per_page": 10,
            "total": 25,
            "from": 1,
            "to": 10
        }
    }
}
```

---

### 2. 获取订单详情

**接口地址**: `GET /api/mlk/dashboard/order-detail`

**接口描述**: 获取指定订单的详细信息，包括商品列表、地址信息、支付信息和物流信息

#### 📥 请求参数

| 参数名称 | 类型 | 必填 | 说明 | 示例值 |
|---------|------|------|------|-------|
| order_id | integer | 是 | 订单ID | 1 |

#### 📤 响应字段说明

**基础响应结构**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| success | boolean | 请求是否成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |

**订单基本信息 (order)**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| id | integer | 订单内部ID |
| increment_id | string | 订单编号（用户可见） |
| status | string | 订单状态代码 |
| status_label | string | 订单状态显示名称 |
| channel_name | string | 销售渠道名称 |
| is_guest | boolean | 是否为游客订单 |
| customer_email | string | 客户邮箱 |
| customer_full_name | string | 客户全名 |
| shipping_method | string | 运输方式代码 |
| shipping_title | string | 运输方式名称 |
| coupon_code | string\|null | 优惠券代码 |
| total_item_count | integer | 商品种类总数 |
| total_qty_ordered | integer | 商品数量总计 |
| currency_code | string | 订单货币代码 |
| grand_total | decimal | 订单总金额 |
| sub_total | decimal | 商品小计 |
| tax_amount | decimal | 税费金额 |
| shipping_amount | decimal | 运费金额 |
| created_at | datetime | 订单创建时间（ISO格式） |
| updated_at | datetime | 订单更新时间（ISO格式） |

**商品信息 (items)**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| items | array | 订单商品列表 |
| items[].id | integer | 订单商品ID |
| items[].sku | string | 商品SKU |
| items[].name | string | 商品名称 |
| items[].qty_ordered | integer | 订购数量 |
| items[].qty_shipped | integer | 已发货数量 |
| items[].qty_invoiced | integer | 已开票数量 |
| items[].qty_canceled | integer | 已取消数量 |
| items[].qty_refunded | integer | 已退款数量 |
| items[].price | decimal | 单价 |
| items[].total | decimal | 商品小计 |
| items[].weight | decimal | 单个商品重量 |
| items[].total_weight | decimal | 商品总重量 |

**账单地址 (billing_address)**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| billing_address | object\|null | 账单地址信息 |
| billing_address.first_name | string | 名 |
| billing_address.last_name | string | 姓 |
| billing_address.company_name | string\|null | 公司名称 |
| billing_address.address | string | 详细地址 |
| billing_address.city | string | 城市 |
| billing_address.state | string | 州/省 |
| billing_address.country | string | 国家代码 |
| billing_address.postcode | string | 邮政编码 |
| billing_address.email | string | 邮箱地址 |
| billing_address.phone | string | 联系电话 |

**收货地址 (shipping_address)**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| shipping_address | object\|null | 收货地址信息 |
| shipping_address.first_name | string | 名 |
| shipping_address.last_name | string | 姓 |
| shipping_address.company_name | string\|null | 公司名称 |
| shipping_address.address | string | 详细地址 |
| shipping_address.city | string | 城市 |
| shipping_address.state | string | 州/省 |
| shipping_address.country | string | 国家代码 |
| shipping_address.postcode | string | 邮政编码 |
| shipping_address.email | string | 邮箱地址 |
| shipping_address.phone | string | 联系电话 |

**物流信息 (shipments)**

| 字段名称 | 类型 | 说明 |
|---------|------|------|
| shipments | array | 物流记录列表 |
| shipments[].id | integer | 物流记录ID |
| shipments[].status | string | 物流状态 |
| shipments[].track_number | string\|null | 追踪号码 |
| shipments[].carrier_code | string | 承运商代码 |
| shipments[].carrier_title | string | 承运商名称 |
| shipments[].created_at | datetime | 发货时间 |

#### 📤 响应示例

**说明**: 以下示例展示了一个已完成订单的详细信息，包含商品、地址和物流信息。

```json
{
    "success": true,      // 请求成功
    "message": "Success", // 响应消息
    "data": {
        "order": {
            "id": 1,
            "increment_id": "ORD-A123456",
            "status": "completed",
            "status_label": "Completed",
            "channel_name": "Default",
            "is_guest": false,
            "customer_email": "<EMAIL>",
            "customer_full_name": "John Doe",
            "shipping_method": "flatrate_flatrate",
            "shipping_title": "Flat Rate",
            "coupon_code": null,
            "total_item_count": 2,
            "total_qty_ordered": 3,
            "currency_code": "USD",
            "grand_total": 156.89,
            "sub_total": 140.00,
            "tax_amount": 11.20,
            "shipping_amount": 5.69,
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T14:20:00.000000Z"
        },
        "items": [
            {
                "id": 1,
                "sku": "PROD001",
                "name": "Sample Product 1",
                "qty_ordered": 2,
                "qty_shipped": 2,
                "qty_invoiced": 2,
                "qty_canceled": 0,
                "qty_refunded": 0,
                "price": 50.00,
                "total": 100.00,
                "weight": 1.5,
                "total_weight": 3.0
            },
            {
                "id": 2,
                "sku": "PROD002", 
                "name": "Sample Product 2",
                "qty_ordered": 1,
                "qty_shipped": 1,
                "qty_invoiced": 1,
                "qty_canceled": 0,
                "qty_refunded": 0,
                "price": 40.00,
                "total": 40.00,
                "weight": 0.8,
                "total_weight": 0.8
            }
        ],
        "billing_address": {
            "first_name": "John",
            "last_name": "Doe",
            "company_name": null,
            "address": "123 Main Street",
            "city": "New York",
            "state": "NY",
            "country": "US",
            "postcode": "10001",
            "email": "<EMAIL>",
            "phone": "+1234567890"
        },
        "shipping_address": {
            "first_name": "John",
            "last_name": "Doe",
            "company_name": null,
            "address": "123 Main Street",
            "city": "New York",
            "state": "NY", 
            "country": "US",
            "postcode": "10001",
            "email": "<EMAIL>",
            "phone": "+1234567890"
        },
        "shipments": [
            {
                "id": 1,
                "status": "shipped",
                "track_number": "TRK123456789",
                "carrier_code": "flatrate",
                "carrier_title": "Flat Rate",
                "created_at": "2024-01-15T12:00:00.000000Z"
            }
        ]
    }
}
```

---

## 🔄 错误响应

### 认证失败
```json
{
    "success": false,
    "message": "Unauthenticated.",
    "code": 401
}
```

### 参数验证失败
```json
{
    "success": false,
    "message": "The order_id field is required.",
    "code": 422
}
```

### 订单不存在或无权访问
```json
{
    "success": false,
    "message": "订单不存在或无权访问",
    "code": 404
}
```

### 服务器错误
```json
{
    "success": false,
    "message": "Internal server error",
    "code": 500
}
```

---

## 📝 使用示例

### JavaScript/Axios 示例

```javascript
// 获取订单列表
const getOrders = async (params = {}) => {
    try {
        const response = await axios.get('/api/mlk/dashboard/orders', {
            params: {
                page: 1,
                per_page: 10,
                status: 'completed',
                order_by: 'created_at',
                sort: 'desc',
                ...params
            },
            headers: {
                'Authorization': 'Bearer ' + accessToken,
                'Accept': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('获取订单列表失败:', error.response?.data?.message);
        throw error;
    }
};

// 获取订单详情
const getOrderDetail = async (orderId) => {
    try {
        const response = await axios.get('/api/mlk/dashboard/order-detail', {
            params: { order_id: orderId },
            headers: {
                'Authorization': 'Bearer ' + accessToken,
                'Accept': 'application/json'
            }
        });
        return response.data;
    } catch (error) {
        console.error('获取订单详情失败:', error.response?.data?.message);
        throw error;
    }
};
```

### cURL 示例

```bash
# 获取订单列表
curl -X GET "http://your-domain.com/api/mlk/dashboard/orders?page=1&per_page=10&status=completed" \
  -H "Authorization: Bearer your_access_token" \
  -H "Accept: application/json"

# 获取订单详情
curl -X GET "http://your-domain.com/api/mlk/dashboard/order-detail?order_id=1" \
  -H "Authorization: Bearer your_access_token" \
  -H "Accept: application/json"
```

---

## 🎯 功能特性

### ✅ 订单列表功能
- **分页支持**: 自定义每页数量，最大50条
- **状态筛选**: 支持按订单状态筛选
- **排序功能**: 支持多字段排序（时间、金额、状态等）
- **完整产品信息**: 包含每个订单商品的详细产品信息
  - 产品基本信息（ID、名称、SKU、类型、价格）
  - 产品图片列表和基础图片URL
  - 格式化价格显示
  - 订单商品数量、重量等详细信息

### ✅ 订单详情功能  
- **完整信息**: 订单基本信息、商品列表、地址、支付、物流
- **商品详情**: 包含SKU、数量、价格、重量等详细信息
- **地址信息**: 完整的账单地址和收货地址
- **物流跟踪**: 运输状态和追踪号码
- **权限验证**: 确保用户只能查看自己的订单

### ✅ 安全特性
- **用户认证**: 所有接口需要Bearer Token认证
- **权限控制**: 用户只能查看自己的订单
- **参数验证**: 严格的输入参数验证
- **错误处理**: 统一的错误响应格式

---

## 📖 字段类型说明

### 数据类型定义

| 类型 | 说明 | 示例 |
|------|------|------|
| integer | 整数 | `1`, `123`, `0` |
| string | 字符串 | `"Order Name"`, `"ORD-123456"` |
| decimal | 小数（金额类） | `156.89`, `50.00` |
| boolean | 布尔值 | `true`, `false` |
| datetime | 日期时间（ISO格式） | `"2024-01-15T10:30:00.000000Z"` |
| array | 数组 | `[...]` |
| object | 对象 | `{...}` |
| null | 空值 | `null` |

### 特殊字段说明

- **货币金额**: 所有金额字段均为decimal类型，保留2位小数
- **图片URL**: 支持多种格式（webp、jpg、png等），可能为null
- **日期时间**: 使用UTC时间，ISO 8601格式
- **状态字段**: 使用预定义的枚举值，详见状态枚举说明
- **可选字段**: 标记为`|null`的字段在某些情况下可能为空

## 🚀 最佳实践

1. **分页使用**: 建议使用合适的分页大小，避免一次获取过多数据
2. **状态筛选**: 根据业务需要使用状态筛选提高查询效率
3. **错误处理**: 前端应当妥善处理各种错误响应
4. **缓存策略**: 可以对订单列表进行适当缓存
5. **Loading状态**: 查询订单详情时显示加载状态提升用户体验
6. **图片处理**: 产品图片可能为空，前端应显示默认占位图
7. **金额显示**: 建议使用formatted_price字段显示格式化金额
8. **时间显示**: 可使用datetime字段显示人性化时间 